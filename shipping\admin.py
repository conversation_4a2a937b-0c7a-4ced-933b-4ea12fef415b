from django.contrib import admin
from .models import ShippingCompany, ShippingZone, ShippingRate, Shipment, ShipmentTracking


class ShipmentTrackingInline(admin.TabularInline):
    model = ShipmentTracking
    extra = 0
    readonly_fields = ['timestamp']


@admin.register(ShippingCompany)
class ShippingCompanyAdmin(admin.ModelAdmin):
    list_display = ['name', 'phone', 'email', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'phone', 'email']
    list_editable = ['is_active']


@admin.register(ShippingZone)
class ShippingZoneAdmin(admin.ModelAdmin):
    list_display = ['name', 'zone_type', 'is_active']
    list_filter = ['zone_type', 'is_active']
    search_fields = ['name']
    list_editable = ['is_active']


@admin.register(ShippingRate)
class ShippingRateAdmin(admin.ModelAdmin):
    list_display = ['company', 'zone', 'base_price', 'delivery_days_min', 'delivery_days_max', 'is_active']
    list_filter = ['company', 'zone', 'is_active']
    search_fields = ['company__name', 'zone__name']
    list_editable = ['base_price', 'is_active']


@admin.register(Shipment)
class ShipmentAdmin(admin.ModelAdmin):
    list_display = ['order', 'company', 'tracking_number', 'status', 'shipping_cost', 'created_at']
    list_filter = ['company', 'status', 'created_at']
    search_fields = ['order__order_number', 'tracking_number', 'order__customer__first_name']
    list_editable = ['status']
    inlines = [ShipmentTrackingInline]

    fieldsets = (
        ('معلومات الشحنة', {
            'fields': ('order', 'company', 'tracking_number', 'status')
        }),
        ('تفاصيل الشحنة', {
            'fields': ('weight', 'shipping_cost', 'notes')
        }),
        ('تواريخ', {
            'fields': ('picked_up_at', 'delivered_at')
        }),
    )


@admin.register(ShipmentTracking)
class ShipmentTrackingAdmin(admin.ModelAdmin):
    list_display = ['shipment', 'status', 'location', 'timestamp']
    list_filter = ['status', 'timestamp']
    search_fields = ['shipment__order__order_number', 'location']
    readonly_fields = ['timestamp']
