from django.urls import path
from . import views

app_name = 'shipping'

urlpatterns = [
    path('', views.ShipmentListView.as_view(), name='list'),

    # شركات الشحن
    path('companies/', views.ShippingCompanyListView.as_view(), name='companies'),
    path('companies/create/', views.ShippingCompanyCreateView.as_view(), name='company_create'),
    path('companies/<int:pk>/', views.ShippingCompanyDetailView.as_view(), name='company_detail'),
    path('companies/<int:pk>/update/', views.ShippingCompanyUpdateView.as_view(), name='company_update'),
    path('companies/<int:pk>/delete/', views.ShippingCompanyDeleteView.as_view(), name='company_delete'),

    # أسعار الشحن
    path('rates/', views.ShippingRateListView.as_view(), name='rates'),
    path('rates/create/', views.ShippingRateCreateView.as_view(), name='rate_create'),
    path('rates/<int:pk>/update/', views.ShippingRateUpdateView.as_view(), name='rate_update'),
    path('rates/<int:pk>/delete/', views.ShippingRateDeleteView.as_view(), name='rate_delete'),

    # الشحنات
    path('<int:pk>/', views.ShipmentDetailView.as_view(), name='detail'),
    path('<int:pk>/track/', views.ShipmentTrackingView.as_view(), name='track'),
    path('<int:pk>/label/', views.ShippingLabelView.as_view(), name='label'),
]
