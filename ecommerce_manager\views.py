from django.shortcuts import render
from django.utils import timezone
from django.db.models import Sum, Count, F
from datetime import datetime, timedelta
import json

from products.models import Product
from orders.models import Order
from customers.models import Customer


def dashboard(request):
    """عرض لوحة التحكم الرئيسية"""
    today = timezone.now().date()
    
    # إحصائيات عامة
    total_orders = Order.objects.count()
    total_revenue = Order.objects.filter(status='delivered').aggregate(
        total=Sum('total_amount')
    )['total'] or 0
    total_customers = Customer.objects.count()
    total_products = Product.objects.count()
    
    # آخر الطلبيات
    recent_orders = Order.objects.select_related('customer').order_by('-created_at')[:5]
    
    # المنتجات منخفضة المخزون
    low_stock_products = Product.objects.filter(
        quantity__lte=F('min_quantity')
    ).order_by('quantity')[:10]
    
    # بيانات المبيعات لآخر 7 أيام
    sales_data = []
    sales_labels = []
    
    for i in range(6, -1, -1):
        date = today - timedelta(days=i)
        daily_sales = Order.objects.filter(
            created_at__date=date,
            status='delivered'
        ).aggregate(total=Sum('total_amount'))['total'] or 0
        
        sales_data.append(float(daily_sales))
        sales_labels.append(date.strftime('%d/%m'))
    
    context = {
        'today': today,
        'total_orders': total_orders,
        'total_revenue': total_revenue,
        'total_customers': total_customers,
        'total_products': total_products,
        'recent_orders': recent_orders,
        'low_stock_products': low_stock_products,
        'sales_data': json.dumps(sales_data),
        'sales_labels': json.dumps(sales_labels),
    }
    
    return render(request, 'dashboard.html', context)
