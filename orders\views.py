from django.shortcuts import render, get_object_or_404, redirect
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.contrib import messages
from django.urls import reverse_lazy
from django.db.models import Q, Sum
from django.http import JsonResponse, HttpResponse
from django.utils import timezone

from .models import Order, OrderItem, OrderStatusHistory
from .forms import OrderForm, OrderItemFormSet, OrderStatusForm
from customers.models import Customer
from products.models import Product


class OrderListView(ListView):
    """عرض قائمة الطلبيات"""
    model = Order
    template_name = 'orders/list.html'
    context_object_name = 'orders'
    paginate_by = 20

    def get_queryset(self):
        queryset = Order.objects.select_related('customer').prefetch_related('items').order_by('-created_at')

        # البحث
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(order_number__icontains=search) |
                Q(customer__first_name__icontains=search) |
                Q(customer__last_name__icontains=search) |
                Q(customer__phone__icontains=search)
            )

        # تصفية حسب الحالة
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)

        # تصفية حسب حالة الدفع
        payment_status = self.request.GET.get('payment_status')
        if payment_status:
            queryset = queryset.filter(payment_status=payment_status)

        # تصفية حسب التاريخ
        date_from = self.request.GET.get('date_from')
        date_to = self.request.GET.get('date_to')
        if date_from:
            queryset = queryset.filter(created_at__date__gte=date_from)
        if date_to:
            queryset = queryset.filter(created_at__date__lte=date_to)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['status_choices'] = Order.STATUS_CHOICES
        context['payment_status_choices'] = Order.PAYMENT_STATUS_CHOICES
        context['search'] = self.request.GET.get('search', '')
        context['selected_status'] = self.request.GET.get('status', '')
        context['selected_payment_status'] = self.request.GET.get('payment_status', '')
        context['date_from'] = self.request.GET.get('date_from', '')
        context['date_to'] = self.request.GET.get('date_to', '')

        # إحصائيات سريعة
        context['total_orders'] = self.get_queryset().count()
        context['pending_orders'] = self.get_queryset().filter(status='new').count()
        context['completed_orders'] = self.get_queryset().filter(status='delivered').count()

        return context


class OrderDetailView(DetailView):
    """عرض تفاصيل الطلب"""
    model = Order
    template_name = 'orders/detail.html'
    context_object_name = 'order'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['status_history'] = self.object.status_history.all()
        return context


class OrderCreateView(CreateView):
    """إضافة طلب جديد"""
    model = Order
    form_class = OrderForm
    template_name = 'orders/form.html'
    success_url = reverse_lazy('orders:list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'إضافة طلب جديد'
        if self.request.POST:
            context['formset'] = OrderItemFormSet(self.request.POST)
        else:
            context['formset'] = OrderItemFormSet(queryset=OrderItem.objects.none())
        return context

    def form_valid(self, form):
        context = self.get_context_data()
        formset = context['formset']

        if formset.is_valid():
            self.object = form.save()
            formset.instance = self.object

            # حفظ العناصر الصحيحة فقط
            for form_item in formset:
                if form_item.cleaned_data and not form_item.cleaned_data.get('DELETE', False):
                    if form_item.cleaned_data.get('product') and form_item.cleaned_data.get('quantity'):
                        form_item.save()

            # إنشاء سجل في تاريخ الحالات
            OrderStatusHistory.objects.create(
                order=self.object,
                status=self.object.status,
                notes='تم إنشاء الطلب'
            )

            messages.success(self.request, f'تم إنشاء الطلب #{self.object.order_number} بنجاح')
            return super().form_valid(form)
        else:
            return self.render_to_response(self.get_context_data(form=form))


class OrderUpdateView(UpdateView):
    """تعديل الطلب"""
    model = Order
    form_class = OrderForm
    template_name = 'orders/form.html'
    success_url = reverse_lazy('orders:list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = f'تعديل الطلب #{self.object.order_number}'
        if self.request.POST:
            context['formset'] = OrderItemFormSet(self.request.POST, instance=self.object)
        else:
            context['formset'] = OrderItemFormSet(instance=self.object)
        return context

    def form_valid(self, form):
        context = self.get_context_data()
        formset = context['formset']

        if formset.is_valid():
            old_status = Order.objects.get(pk=self.object.pk).status
            self.object = form.save()
            formset.save()

            # إضافة سجل في تاريخ الحالات إذا تغيرت الحالة
            if old_status != self.object.status:
                OrderStatusHistory.objects.create(
                    order=self.object,
                    status=self.object.status,
                    notes='تم تحديث حالة الطلب'
                )

            messages.success(self.request, f'تم تحديث الطلب #{self.object.order_number} بنجاح')
            return super().form_valid(form)
        else:
            return self.render_to_response(self.get_context_data(form=form))


class OrderDeleteView(DeleteView):
    """حذف الطلب"""
    model = Order
    template_name = 'orders/delete.html'
    success_url = reverse_lazy('orders:list')

    def delete(self, request, *args, **kwargs):
        order = self.get_object()
        messages.success(request, f'تم حذف الطلب #{order.order_number} بنجاح')
        return super().delete(request, *args, **kwargs)


class OrderStatusUpdateView(UpdateView):
    """تحديث حالة الطلب"""
    model = Order
    form_class = OrderStatusForm
    template_name = 'orders/status_update.html'

    def form_valid(self, form):
        old_status = Order.objects.get(pk=self.object.pk).status
        response = super().form_valid(form)

        # إضافة سجل في تاريخ الحالات
        OrderStatusHistory.objects.create(
            order=self.object,
            status=self.object.status,
            notes=form.cleaned_data.get('notes', '')
        )

        # تحديث تواريخ خاصة حسب الحالة
        if self.object.status == 'shipped' and not self.object.shipped_at:
            self.object.shipped_at = timezone.now()
            self.object.save()
        elif self.object.status == 'delivered' and not self.object.delivered_at:
            self.object.delivered_at = timezone.now()
            self.object.save()

        messages.success(self.request, f'تم تحديث حالة الطلب #{self.object.order_number} بنجاح')

        if self.request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({'success': True, 'message': 'تم تحديث الحالة بنجاح'})

        return redirect('orders:detail', pk=self.object.pk)


class OrderInvoiceView(DetailView):
    """عرض فاتورة الطلب"""
    model = Order
    template_name = 'orders/invoice.html'
    context_object_name = 'order'


class OrderPrintView(DetailView):
    """طباعة الطلب"""
    model = Order
    template_name = 'orders/print.html'
    context_object_name = 'order'
