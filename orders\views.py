from django.shortcuts import render
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.http import HttpResponse

# Views مؤقتة للاختبار
class OrderListView(ListView):
    template_name = 'orders/list.html'
    def get(self, request):
        return HttpResponse("صفحة الطلبيات - قيد التطوير")

class OrderDetailView(DetailView):
    def get(self, request, pk):
        return HttpResponse("تفاصيل الطلب - قيد التطوير")

class OrderCreateView(CreateView):
    def get(self, request):
        return HttpResponse("إضافة طلب جديد - قيد التطوير")

class OrderUpdateView(UpdateView):
    def get(self, request, pk):
        return HttpResponse("تعديل الطلب - قيد التطوير")

class OrderDeleteView(DeleteView):
    def get(self, request, pk):
        return HttpResponse("حذف الطلب - قيد التطوير")

class OrderStatusUpdateView(UpdateView):
    def get(self, request, pk):
        return HttpResponse("تحديث حالة الطلب - قيد التطوير")

class OrderInvoiceView(DetailView):
    def get(self, request, pk):
        return HttpResponse("فاتورة الطلب - قيد التطوير")

class OrderPrintView(DetailView):
    def get(self, request, pk):
        return HttpResponse("طباعة الطلب - قيد التطوير")
