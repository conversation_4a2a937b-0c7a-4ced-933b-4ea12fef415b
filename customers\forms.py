from django import forms
from .models import Customer, Address


class CustomerForm(forms.ModelForm):
    """نموذج إضافة/تعديل الزبون"""
    
    class Meta:
        model = Customer
        fields = [
            'first_name', 'last_name', 'phone', 'email', 'gender',
            'birth_date', 'notes', 'is_active'
        ]
        widgets = {
            'first_name': forms.TextInput(attrs={'placeholder': 'الاسم الأول'}),
            'last_name': forms.TextInput(attrs={'placeholder': 'اسم العائلة'}),
            'phone': forms.TextInput(attrs={'placeholder': 'رقم الهاتف'}),
            'email': forms.EmailInput(attrs={'placeholder': 'البريد الإلكتروني (اختياري)'}),
            'birth_date': forms.DateInput(attrs={'type': 'date'}),
            'notes': forms.Textarea(attrs={'rows': 4, 'placeholder': 'ملاحظات حول الزبون'}),
        }
        labels = {
            'first_name': 'الاسم الأول',
            'last_name': 'اسم العائلة',
            'phone': 'رقم الهاتف',
            'email': 'البريد الإلكتروني',
            'gender': 'الجنس',
            'birth_date': 'تاريخ الميلاد',
            'notes': 'ملاحظات',
            'is_active': 'نشط',
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # إضافة CSS classes للحقول
        for field_name, field in self.fields.items():
            if field_name == 'is_active':
                field.widget.attrs['class'] = 'form-check-input'
            elif field_name == 'notes':
                field.widget.attrs['class'] = 'form-control'
            else:
                field.widget.attrs['class'] = 'form-control'
    
    def clean_phone(self):
        """التحقق من عدم تكرار رقم الهاتف"""
        phone = self.cleaned_data.get('phone')
        if phone:
            existing = Customer.objects.filter(phone=phone)
            if self.instance.pk:
                existing = existing.exclude(pk=self.instance.pk)
            if existing.exists():
                raise forms.ValidationError('رقم الهاتف موجود مسبقاً')
        return phone
    
    def clean_email(self):
        """التحقق من صحة البريد الإلكتروني وعدم تكراره"""
        email = self.cleaned_data.get('email')
        if email:
            existing = Customer.objects.filter(email=email)
            if self.instance.pk:
                existing = existing.exclude(pk=self.instance.pk)
            if existing.exists():
                raise forms.ValidationError('البريد الإلكتروني موجود مسبقاً')
        return email


class AddressForm(forms.ModelForm):
    """نموذج إضافة/تعديل العنوان"""
    
    class Meta:
        model = Address
        fields = ['street_address', 'city', 'wilaya', 'postal_code', 'is_default']
        widgets = {
            'street_address': forms.Textarea(attrs={'rows': 3, 'placeholder': 'العنوان التفصيلي'}),
            'city': forms.TextInput(attrs={'placeholder': 'المدينة'}),
            'postal_code': forms.TextInput(attrs={'placeholder': 'الرمز البريدي (اختياري)'}),
        }
        labels = {
            'street_address': 'العنوان',
            'city': 'المدينة',
            'wilaya': 'الولاية',
            'postal_code': 'الرمز البريدي',
            'is_default': 'العنوان الافتراضي',
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        
        self.helper.layout = Layout(
            'street_address',
            Row(
                Column('city', css_class='col-md-6'),
                Column('wilaya', css_class='col-md-6'),
            ),
            Row(
                Column('postal_code', css_class='col-md-6'),
                Column(
                    Field('is_default', wrapper_class='form-check form-switch'),
                    css_class='col-md-6 d-flex align-items-center'
                ),
            ),
            HTML('<hr>'),
            Row(
                Column(
                    Submit('submit', 'حفظ العنوان', css_class='btn btn-primary'),
                    css_class='col-auto'
                ),
                Column(
                    HTML('<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>'),
                    css_class='col-auto'
                ),
            )
        )


class CustomerSearchForm(forms.Form):
    """نموذج البحث في الزبائن"""
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'placeholder': 'البحث في الزبائن...',
            'class': 'form-control'
        })
    )
    status = forms.ChoiceField(
        choices=[
            ('', 'جميع الزبائن'),
            ('active', 'نشط'),
            ('inactive', 'غير نشط'),
        ],
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    gender = forms.ChoiceField(
        choices=[('', 'جميع الأجناس')] + Customer.GENDER_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )


class QuickCustomerForm(forms.Form):
    """نموذج إضافة زبون سريع"""
    first_name = forms.CharField(
        label='الاسم الأول',
        max_length=50,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'الاسم الأول'})
    )
    last_name = forms.CharField(
        label='اسم العائلة',
        max_length=50,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم العائلة'})
    )
    phone = forms.CharField(
        label='رقم الهاتف',
        max_length=20,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رقم الهاتف'})
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        
        self.helper.layout = Layout(
            'first_name',
            'last_name',
            'phone',
            HTML('<hr>'),
            Submit('submit', 'إضافة الزبون', css_class='btn btn-primary w-100')
        )
    
    def clean_phone(self):
        """التحقق من عدم تكرار رقم الهاتف"""
        phone = self.cleaned_data.get('phone')
        if phone and Customer.objects.filter(phone=phone).exists():
            raise forms.ValidationError('رقم الهاتف موجود مسبقاً')
        return phone
    
    def save(self):
        """حفظ الزبون الجديد"""
        return Customer.objects.create(
            first_name=self.cleaned_data['first_name'],
            last_name=self.cleaned_data['last_name'],
            phone=self.cleaned_data['phone']
        )
