from django.shortcuts import render
from django.views.generic import List<PERSON>iew, DetailView, CreateView, UpdateView, DeleteView
from django.http import HttpResponse

# Views مؤقتة للاختبار
class SettingsView(ListView):
    def get(self, request):
        return HttpResponse("الإعدادات العامة - قيد التطوير")

class StoreSettingsView(ListView):
    def get(self, request):
        return HttpResponse("إعدادات المتجر - قيد التطوير")

class OrderStatusListView(ListView):
    def get(self, request):
        return HttpResponse("حالات الطلبيات - قيد التطوير")

class PaymentMethodListView(ListView):
    def get(self, request):
        return HttpResponse("طرق الدفع - قيد التطوير")
