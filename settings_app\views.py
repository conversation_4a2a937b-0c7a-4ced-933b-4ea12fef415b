from django.shortcuts import render, redirect
from django.views.generic import ListView, TemplateView, UpdateView
from django.contrib import messages
from django.urls import reverse_lazy

from .models import StoreSettings, OrderStatus, PaymentMethod
from .forms import StoreSettingsForm


class SettingsView(TemplateView):
    """الإعدادات العامة"""
    template_name = 'settings/index.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['store_settings'] = StoreSettings.get_settings()
        context['order_statuses'] = OrderStatus.objects.filter(is_active=True).order_by('order')
        context['payment_methods'] = PaymentMethod.objects.filter(is_active=True).order_by('name')
        return context


class StoreSettingsView(UpdateView):
    """إعدادات المتجر"""
    model = StoreSettings
    form_class = StoreSettingsForm
    template_name = 'settings/store.html'
    success_url = reverse_lazy('settings:store')

    def get_object(self):
        return StoreSettings.get_settings()

    def form_valid(self, form):
        messages.success(self.request, 'تم حفظ إعدادات المتجر بنجاح')
        return super().form_valid(form)


class OrderStatusListView(ListView):
    """قائمة حالات الطلبيات"""
    model = OrderStatus
    template_name = 'settings/order_statuses.html'
    context_object_name = 'statuses'

    def get_queryset(self):
        return OrderStatus.objects.order_by('order', 'name')


class PaymentMethodListView(ListView):
    """قائمة طرق الدفع"""
    model = PaymentMethod
    template_name = 'settings/payment_methods.html'
    context_object_name = 'methods'

    def get_queryset(self):
        return PaymentMethod.objects.order_by('name')
