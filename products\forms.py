from django import forms
from .models import Product, Category


class ProductForm(forms.ModelForm):
    """نموذج إضافة/تعديل المنتج"""
    
    class Meta:
        model = Product
        fields = [
            'name', 'description', 'category', 'price', 'cost_price',
            'quantity', 'min_quantity', 'image', 'sku', 'is_active'
        ]
        widgets = {
            'name': forms.TextInput(attrs={'placeholder': 'اسم المنتج'}),
            'description': forms.Textarea(attrs={'rows': 4, 'placeholder': 'وصف المنتج'}),
            'price': forms.NumberInput(attrs={'step': '0.01', 'min': '0'}),
            'cost_price': forms.NumberInput(attrs={'step': '0.01', 'min': '0'}),
            'quantity': forms.NumberInput(attrs={'min': '0'}),
            'min_quantity': forms.NumberInput(attrs={'min': '1'}),
            'sku': forms.TextInput(attrs={'placeholder': 'رمز المنتج (اختياري)'}),
        }
        labels = {
            'name': 'اسم المنتج',
            'description': 'وصف المنتج',
            'category': 'الفئة',
            'price': 'سعر البيع (دج)',
            'cost_price': 'سعر التكلفة (دج)',
            'quantity': 'الكمية المتوفرة',
            'min_quantity': 'الحد الأدنى للكمية',
            'image': 'صورة المنتج',
            'sku': 'رمز المنتج',
            'is_active': 'نشط',
        }
        help_texts = {
            'min_quantity': 'سيتم إرسال تنبيه عند الوصول لهذا الحد',
            'cost_price': 'سعر التكلفة لحساب الأرباح (اختياري)',
            'sku': 'رمز فريد للمنتج (اختياري)',
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # إضافة CSS classes للحقول
        for field_name, field in self.fields.items():
            if field_name == 'is_active':
                field.widget.attrs['class'] = 'form-check-input'
            elif field_name == 'description':
                field.widget.attrs['class'] = 'form-control'
            elif field_name == 'image':
                field.widget.attrs['class'] = 'form-control'
            else:
                field.widget.attrs['class'] = 'form-control'
    
    def clean_sku(self):
        """التحقق من عدم تكرار رمز المنتج"""
        sku = self.cleaned_data.get('sku')
        if sku:
            existing = Product.objects.filter(sku=sku)
            if self.instance.pk:
                existing = existing.exclude(pk=self.instance.pk)
            if existing.exists():
                raise forms.ValidationError('رمز المنتج موجود مسبقاً')
        return sku
    
    def clean(self):
        """التحقق من صحة البيانات"""
        cleaned_data = super().clean()
        price = cleaned_data.get('price')
        cost_price = cleaned_data.get('cost_price')
        
        if cost_price and price and cost_price > price:
            raise forms.ValidationError('سعر التكلفة لا يمكن أن يكون أكبر من سعر البيع')
        
        return cleaned_data


class CategoryForm(forms.ModelForm):
    """نموذج إضافة/تعديل الفئة"""
    
    class Meta:
        model = Category
        fields = ['name', 'description']
        widgets = {
            'name': forms.TextInput(attrs={'placeholder': 'اسم الفئة'}),
            'description': forms.Textarea(attrs={'rows': 3, 'placeholder': 'وصف الفئة (اختياري)'}),
        }
        labels = {
            'name': 'اسم الفئة',
            'description': 'وصف الفئة',
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        
        self.helper.layout = Layout(
            'name',
            'description',
            HTML('<hr>'),
            Row(
                Column(
                    Submit('submit', 'حفظ الفئة', css_class='btn btn-primary'),
                    css_class='col-auto'
                ),
                Column(
                    HTML('<a href="{% url "products:categories" %}" class="btn btn-secondary">إلغاء</a>'),
                    css_class='col-auto'
                ),
            )
        )


class ProductSearchForm(forms.Form):
    """نموذج البحث في المنتجات"""
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'placeholder': 'البحث في المنتجات...',
            'class': 'form-control'
        })
    )
    category = forms.ModelChoiceField(
        queryset=Category.objects.all(),
        required=False,
        empty_label='جميع الفئات',
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    stock_status = forms.ChoiceField(
        choices=[
            ('', 'جميع المنتجات'),
            ('low', 'مخزون منخفض'),
            ('out', 'نفد من المخزون'),
        ],
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
