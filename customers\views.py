from django.shortcuts import render, get_object_or_404, redirect
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.contrib import messages
from django.urls import reverse_lazy
from django.db.models import Q, Sum, Count
from django.http import JsonResponse
from django.utils import timezone

from .models import Customer, Address
from .forms import CustomerForm, AddressForm
from orders.models import Order


class CustomerListView(ListView):
    """عرض قائمة الزبائن"""
    model = Customer
    template_name = 'customers/list.html'
    context_object_name = 'customers'
    paginate_by = 20

    def get_queryset(self):
        queryset = Customer.objects.annotate(
            orders_count=Count('orders'),
            spent_amount=Sum('orders__total_amount')
        ).order_by('-created_at')

        # البحث
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(first_name__icontains=search) |
                Q(last_name__icontains=search) |
                Q(phone__icontains=search) |
                Q(email__icontains=search)
            )

        # تصفية حسب الحالة
        status = self.request.GET.get('status')
        if status == 'active':
            queryset = queryset.filter(is_active=True)
        elif status == 'inactive':
            queryset = queryset.filter(is_active=False)

        # تصفية حسب الجنس
        gender = self.request.GET.get('gender')
        if gender:
            queryset = queryset.filter(gender=gender)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search'] = self.request.GET.get('search', '')
        context['selected_status'] = self.request.GET.get('status', '')
        context['selected_gender'] = self.request.GET.get('gender', '')
        context['gender_choices'] = Customer.GENDER_CHOICES

        # إحصائيات سريعة
        context['total_customers'] = self.get_queryset().count()
        context['active_customers'] = self.get_queryset().filter(is_active=True).count()
        context['new_customers_this_month'] = Customer.objects.filter(
            created_at__month=timezone.now().month
        ).count()

        return context


class CustomerDetailView(DetailView):
    """عرض تفاصيل الزبون"""
    model = Customer
    template_name = 'customers/detail.html'
    context_object_name = 'customer'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['addresses'] = self.object.addresses.all()
        context['recent_orders'] = self.object.orders.order_by('-created_at')[:10]
        context['order_stats'] = self.object.orders.aggregate(
            total_orders=Count('id'),
            total_spent=Sum('total_amount'),
            completed_orders=Count('id', filter=Q(status='delivered'))
        )
        return context


class CustomerCreateView(CreateView):
    """إضافة زبون جديد"""
    model = Customer
    form_class = CustomerForm
    template_name = 'customers/form.html'
    success_url = reverse_lazy('customers:list')

    def form_valid(self, form):
        messages.success(self.request, f'تم إضافة الزبون {form.instance.full_name} بنجاح')
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'إضافة زبون جديد'
        return context


class CustomerUpdateView(UpdateView):
    """تعديل الزبون"""
    model = Customer
    form_class = CustomerForm
    template_name = 'customers/form.html'
    success_url = reverse_lazy('customers:list')

    def form_valid(self, form):
        messages.success(self.request, f'تم تحديث بيانات الزبون {form.instance.full_name} بنجاح')
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = f'تعديل بيانات الزبون: {self.object.full_name}'
        return context


class CustomerDeleteView(DeleteView):
    """حذف الزبون"""
    model = Customer
    template_name = 'customers/delete.html'
    success_url = reverse_lazy('customers:list')

    def delete(self, request, *args, **kwargs):
        customer = self.get_object()
        messages.success(request, f'تم حذف الزبون {customer.full_name} بنجاح')
        return super().delete(request, *args, **kwargs)


class CustomerOrdersView(ListView):
    """عرض طلبيات الزبون"""
    model = Order
    template_name = 'customers/orders.html'
    context_object_name = 'orders'
    paginate_by = 10

    def get_queryset(self):
        self.customer = get_object_or_404(Customer, pk=self.kwargs['pk'])
        return self.customer.orders.order_by('-created_at')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['customer'] = self.customer
        context['order_stats'] = self.get_queryset().aggregate(
            total_orders=Count('id'),
            total_spent=Sum('total_amount'),
            completed_orders=Count('id', filter=Q(status='delivered')),
            pending_orders=Count('id', filter=Q(status__in=['new', 'confirmed']))
        )
        return context
