from django.shortcuts import render
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.http import HttpResponse

# Views مؤقتة للاختبار
class CustomerListView(ListView):
    def get(self, request):
        return HttpResponse("صفحة الزبائن - قيد التطوير")

class CustomerDetailView(DetailView):
    def get(self, request, pk):
        return HttpResponse("تفاصيل الزبون - قيد التطوير")

class CustomerCreateView(CreateView):
    def get(self, request):
        return HttpResponse("إضافة زبون جديد - قيد التطوير")

class CustomerUpdateView(UpdateView):
    def get(self, request, pk):
        return HttpResponse("تعديل الزبون - قيد التطوير")

class CustomerDeleteView(DeleteView):
    def get(self, request, pk):
        return HttpResponse("حذف الزبون - قيد التطوير")

class CustomerOrdersView(ListView):
    def get(self, request, pk):
        return HttpResponse("طلبيات الزبون - قيد التطوير")
