# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2012,2014
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON>, 2014
# Pet<PERSON> Strand<PERSON> <<EMAIL>>, 2019
# <PERSON>, 2013
# <PERSON> <<EMAIL>>, 2021
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2022-07-24 20:19+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Swedish (http://www.transifex.com/django/django/language/"
"sv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: sv\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Administrative Documentation"
msgstr "Dokumentation för admin"

msgid "Home"
msgstr "Hem"

msgid "Documentation"
msgstr "Dokumentation"

msgid "Bookmarklets"
msgstr "Smarta bokmärken"

msgid "Documentation bookmarklets"
msgstr "Smarta bokmärken för dokumentation"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"För att installera bookmarklets, dra länken till din verkygsrad för "
"bokmärken, eller högerklicka på länken och lägg till den till dina "
"bokmärken. Nu kan du välja din bookmarklet från vilken sida som helst."

msgid "Documentation for this page"
msgstr "Dokumentation för denna sida"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""
"Förflyttar dig från valfri sida till dokumentationen för vyn som genererar "
"den sidan."

msgid "Tags"
msgstr "Taggar"

msgid "List of all the template tags and their functions."
msgstr "En lista på alla malltaggar och deras funktioner."

msgid "Filters"
msgstr "Filter"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"Filter är åtgärder som kan appliceras på variabler i en mall för att ändra "
"utmatningen."

msgid "Models"
msgstr "Modeller"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"Modeller är beskrivningar av alla objekt i systemet och deras tillhörande "
"fält. Varje modell har en lista av fält som kan kommas åt som variabler i "
"mallen."

msgid "Views"
msgstr "Vyer"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"Varje sida på den publika sidan är genererad av en vy. Vyn definierar vilka "
"mallar som används för att generera sidan samt vilka objekt som är "
"tillgängliga i den mallen."

msgid "Tools for your browser to quickly access admin functionality."
msgstr ""
"Verktyg till din webbläsare för snabb tillgång till "
"administrationsfunktioner."

msgid "Please install docutils"
msgstr "Vänligen installera docutils"

#, python-format
msgid ""
"The admin documentation system requires Python’s <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""
"Administrationsdokumentationen kräver Pythons <a href=\"%(link)s\">docutils</"
"a> bibliotek."

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""
"Vänligen be din administratör installera <a href=\"%(link)s\">docutils</a>."

#, python-format
msgid "Model: %(name)s"
msgstr "Modell: %(name)s"

msgid "Fields"
msgstr "Fält"

msgid "Field"
msgstr "Fält"

msgid "Type"
msgstr "Typ"

msgid "Description"
msgstr "Beskrivning"

msgid "Methods with arguments"
msgstr "Metod med argument"

msgid "Method"
msgstr "Metod"

msgid "Arguments"
msgstr "Argument"

msgid "Back to Model documentation"
msgstr "Tillbaka till modell-dokumentation"

msgid "Model documentation"
msgstr "Modelldokumentation"

msgid "Model groups"
msgstr "Modellgrupper"

msgid "Templates"
msgstr "Mallar"

#, python-format
msgid "Template: %(name)s"
msgstr "Mall: %(name)s"

#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr "Mall:<q>%(name)s</q>"

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr "Sökvägen för mall <q>%(name)s</q>:"

msgid "(does not exist)"
msgstr "(finns inte)"

msgid "Back to Documentation"
msgstr "Tillbaka till dokumentation"

msgid "Template filters"
msgstr "Mallfilter"

msgid "Template filter documentation"
msgstr "Mallfilterdokumentation"

msgid "Built-in filters"
msgstr "Inbyggda filter"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"För att använda dessa filter, lägg till <code>%(code)s</code> innan du "
"använder filtret i din mall."

msgid "Template tags"
msgstr "Malltaggar"

msgid "Template tag documentation"
msgstr "Malltaggdokumentation"

msgid "Built-in tags"
msgstr "Inbyggda taggar"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"För att använda dessa taggar, lägg till <code>%(code)s</code> innan du "
"använder taggen i din mall."

#, python-format
msgid "View: %(name)s"
msgstr "Vy: %(name)s"

msgid "Context:"
msgstr "Kontext:"

msgid "Templates:"
msgstr "Mallar:"

msgid "Back to View documentation"
msgstr "Tillbaka till vy-dokumentation"

msgid "View documentation"
msgstr "Titta på dokumentationen"

msgid "Jump to namespace"
msgstr "Hoppa till namespace"

msgid "Empty namespace"
msgstr "Tom namespace"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "Vyer för namespace %(name)s"

msgid "Views by empty namespace"
msgstr "Vyer för tom namespace"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"Vyfunktion: <code>%(full_name)s</code>. Namn: <code>%(url_name)s</code>.\n"

msgid "tag:"
msgstr "tagg:"

msgid "filter:"
msgstr "filter:"

msgid "view:"
msgstr "vy:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "Applikation %(app_label)r hittades inte"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "Modell %(model_name)r hittades inte i applikation %(app_label)r"

msgid "model:"
msgstr "modell:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "det relaterade `%(app_label)s.%(data_type)s`-objektet"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "relaterade `%(app_label)s.%(object_name)s`-objekt"

#, python-format
msgid "all %s"
msgstr "alla %s"

#, python-format
msgid "number of %s"
msgstr "antal %s"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s verkar inte vara ett urlpattern-objekt"
