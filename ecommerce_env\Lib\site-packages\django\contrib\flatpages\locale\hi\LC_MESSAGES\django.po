# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON>uma<PERSON> <<EMAIL>>, 2012
# <PERSON><PERSON> <<EMAIL>>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2011
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-10-09 17:42+0200\n"
"PO-Revision-Date: 2017-09-19 16:40+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Hindi (http://www.transifex.com/django/django/language/hi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: hi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Advanced options"
msgstr "उन्नत विकल्प"

msgid "Flat Pages"
msgstr ""

msgid "URL"
msgstr "URL"

msgid ""
"Example: '/about/contact/'. Make sure to have leading and trailing slashes."
msgstr "अग्रणी है और अनुगामी स्लैश का होना सुनिश्चित करें. उदाहरण: '/about/contact/'"

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr "इस मूल्य में सिर्फ वर्णाक्षर, अंक, बिंदु, रेखांकन, डैश, स्लैश और टिल्ड्स ही होने चाहिए"

msgid "URL is missing a leading slash."
msgstr "यूआरएल से प्रमुख स्लैश गायब है."

msgid "URL is missing a trailing slash."
msgstr "यूआरएल से अनुगामी स्लैश गायब है."

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr "%(site)s साइट के लिए %(url)s यूआरएल के साथ चपटापृष्ट मौजूद है."

msgid "title"
msgstr "शीर्षक"

msgid "content"
msgstr "विषय सूची"

msgid "enable comments"
msgstr "टिप्पणियां सक्षम करें"

msgid "template name"
msgstr "सांचे का नाम"

msgid ""
"Example: 'flatpages/contact_page.html'. If this isn't provided, the system "
"will use 'flatpages/default.html'."
msgstr ""
"उदाहरण: 'flatpages/contact_page.html'. यदि यह जिक्र नहीं किया तो यह प्रणाली "
"'flatpages/default.html' का प्रयोग करेगी. ."

msgid "registration required"
msgstr "पंजीकरण आवश्यक"

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr "अगर इस जाँच की है, केवल लॉग इन करने वालों के लिए पृष्ठ देखने में सक्षम हो जाएगा."

msgid "sites"
msgstr ""

msgid "flat page"
msgstr "चपटा पृष्ट"

msgid "flat pages"
msgstr "चपटे पृष्ट"
