{% extends 'base.html' %}

{% block title %}التقارير والإحصائيات - مدير التجارة الإلكترونية{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item active">التقارير والإحصائيات</li>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">التقارير والإحصائيات</h1>
    <div>
        <a href="{% url 'reports:sales' %}" class="btn btn-outline-primary">
            <i class="fas fa-chart-line me-2"></i>
            تقرير المبيعات
        </a>
        <a href="{% url 'reports:export_orders' %}" class="btn btn-success">
            <i class="fas fa-download me-2"></i>
            تصدير البيانات
        </a>
    </div>
</div>

<!-- الإحصائيات الرئيسية -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stats-number">{{ total_orders }}</div>
                    <div class="stats-label">إجمالي الطلبيات</div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-shopping-cart fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stats-number">{{ total_revenue|floatformat:0 }}</div>
                    <div class="stats-label">إجمالي المبيعات (دج)</div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-money-bill-wave fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stats-number">{{ total_customers }}</div>
                    <div class="stats-label">إجمالي الزبائن</div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-users fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stats-number">{{ total_products }}</div>
                    <div class="stats-label">إجمالي المنتجات</div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-box fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات الفترة الحالية -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calendar me-2"></i>
                    آخر 30 يوم
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <h4 class="text-primary">{{ current_period_orders }}</h4>
                        <p class="text-muted mb-0">طلبية جديدة</p>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">{{ current_period_revenue|floatformat:0 }} دج</h4>
                        <p class="text-muted mb-0">إجمالي المبيعات</p>
                    </div>
                </div>
                <hr>
                <div class="d-flex justify-content-between align-items-center">
                    <span>نمو المبيعات:</span>
                    <span class="{% if revenue_growth >= 0 %}text-success{% else %}text-danger{% endif %}">
                        {% if revenue_growth >= 0 %}+{% endif %}{{ revenue_growth|floatformat:1 }}%
                        <i class="fas fa-arrow-{% if revenue_growth >= 0 %}up{% else %}down{% endif %} ms-1"></i>
                    </span>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    توزيع حالات الطلبيات
                </h5>
            </div>
            <div class="card-body">
                <canvas id="orderStatusChart" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- الرسوم البيانية والتقارير -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    مبيعات آخر 7 أيام
                </h5>
            </div>
            <div class="card-body">
                <canvas id="dailySalesChart" height="100"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-link me-2"></i>
                    روابط سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'reports:sales' %}" class="btn btn-outline-primary">
                        <i class="fas fa-chart-line me-2"></i>
                        تقرير المبيعات التفصيلي
                    </a>
                    <a href="{% url 'reports:products' %}" class="btn btn-outline-info">
                        <i class="fas fa-box me-2"></i>
                        تقرير المنتجات
                    </a>
                    <a href="{% url 'reports:customers' %}" class="btn btn-outline-success">
                        <i class="fas fa-users me-2"></i>
                        تقرير الزبائن
                    </a>
                    <a href="{% url 'reports:export_orders' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-download me-2"></i>
                        تصدير الطلبيات
                    </a>
                    <a href="{% url 'reports:export_products' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-download me-2"></i>
                        تصدير المنتجات
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- المنتجات والزبائن الأفضل -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-star me-2"></i>
                    المنتجات الأكثر مبيعاً
                </h5>
            </div>
            <div class="card-body">
                {% if top_products %}
                    {% for product in top_products %}
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <div>
                            <strong>{{ product.name }}</strong>
                            <br><small class="text-muted">{{ product.total_sold }} قطعة مباعة</small>
                        </div>
                        <span class="badge bg-primary">{{ forloop.counter }}</span>
                    </div>
                    {% if not forloop.last %}<hr class="my-2">{% endif %}
                    {% endfor %}
                {% else %}
                    <p class="text-muted text-center">لا توجد مبيعات بعد</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-crown me-2"></i>
                    أفضل الزبائن
                </h5>
            </div>
            <div class="card-body">
                {% if top_customers %}
                    {% for customer in top_customers %}
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <div>
                            <strong>{{ customer.full_name }}</strong>
                            <br><small class="text-muted">{{ customer.spent_amount|floatformat:2 }} دج</small>
                        </div>
                        <span class="badge bg-success">{{ forloop.counter }}</span>
                    </div>
                    {% if not forloop.last %}<hr class="my-2">{% endif %}
                    {% endfor %}
                {% else %}
                    <p class="text-muted text-center">لا توجد مشتريات بعد</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// رسم بياني للمبيعات اليومية
const dailySalesCtx = document.getElementById('dailySalesChart').getContext('2d');
const dailySalesChart = new Chart(dailySalesCtx, {
    type: 'line',
    data: {
        labels: {{ daily_sales_labels|safe }},
        datasets: [{
            label: 'المبيعات (دج)',
            data: {{ daily_sales_data|safe }},
            borderColor: '#667eea',
            backgroundColor: 'rgba(102, 126, 234, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return value.toLocaleString() + ' دج';
                    }
                }
            }
        }
    }
});

// رسم بياني دائري لحالات الطلبيات
const orderStatusCtx = document.getElementById('orderStatusChart').getContext('2d');
const orderStatusChart = new Chart(orderStatusCtx, {
    type: 'doughnut',
    data: {
        labels: {{ order_status_labels|safe }},
        datasets: [{
            data: {{ order_status_data|safe }},
            backgroundColor: [
                '#17a2b8', '#007bff', '#ffc107', '#fd7e14',
                '#28a745', '#dc3545', '#6c757d'
            ],
            borderWidth: 2,
            borderColor: '#fff'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
</script>
{% endblock %}
