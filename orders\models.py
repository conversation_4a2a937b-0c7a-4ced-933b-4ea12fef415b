from django.db import models
from django.core.validators import MinValueValidator
from django.urls import reverse
from customers.models import Customer
from products.models import Product
from decimal import Decimal


class Order(models.Model):
    """نموذج الطلبيات"""
    STATUS_CHOICES = [
        ('new', 'جديد'),
        ('confirmed', 'مؤكد'),
        ('preparing', 'قيد التحضير'),
        ('shipped', 'تم الإرسال'),
        ('delivered', 'تم التوصيل'),
        ('cancelled', 'ملغى'),
        ('returned', 'مرتجع'),
    ]

    PAYMENT_STATUS_CHOICES = [
        ('pending', 'في الانتظار'),
        ('paid', 'مدفوع'),
        ('partial', 'مدفوع جزئياً'),
        ('refunded', 'مسترد'),
    ]

    order_number = models.CharField('رقم الطلب', max_length=20, unique=True)
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, related_name='orders', verbose_name='الزبون')
    status = models.CharField('حالة الطلب', max_length=20, choices=STATUS_CHOICES, default='new')
    payment_status = models.CharField('حالة الدفع', max_length=20, choices=PAYMENT_STATUS_CHOICES, default='pending')

    # معلومات التوصيل
    shipping_address = models.TextField('عنوان التوصيل')
    shipping_city = models.CharField('مدينة التوصيل', max_length=100)
    shipping_wilaya = models.CharField('ولاية التوصيل', max_length=100)
    shipping_phone = models.CharField('هاتف التوصيل', max_length=20)

    # المبالغ
    subtotal = models.DecimalField('المجموع الفرعي', max_digits=10, decimal_places=2, default=0)
    shipping_cost = models.DecimalField('تكلفة الشحن', max_digits=10, decimal_places=2, default=0)
    discount = models.DecimalField('الخصم', max_digits=10, decimal_places=2, default=0)
    total_amount = models.DecimalField('المبلغ الإجمالي', max_digits=10, decimal_places=2, default=0)

    # ملاحظات وتواريخ
    notes = models.TextField('ملاحظات', blank=True, null=True)
    created_at = models.DateTimeField('تاريخ الإنشاء', auto_now_add=True)
    updated_at = models.DateTimeField('تاريخ التحديث', auto_now=True)
    shipped_at = models.DateTimeField('تاريخ الإرسال', blank=True, null=True)
    delivered_at = models.DateTimeField('تاريخ التوصيل', blank=True, null=True)

    class Meta:
        verbose_name = 'طلب'
        verbose_name_plural = 'الطلبيات'
        ordering = ['-created_at']

    def __str__(self):
        return f"طلب #{self.order_number} - {self.customer.full_name}"

    def get_absolute_url(self):
        return reverse('orders:detail', kwargs={'pk': self.pk})

    def save(self, *args, **kwargs):
        # توليد رقم الطلب تلقائياً
        if not self.order_number:
            last_order = Order.objects.order_by('-id').first()
            if last_order:
                last_number = int(last_order.order_number.split('-')[-1])
                self.order_number = f"ORD-{last_number + 1:06d}"
            else:
                self.order_number = "ORD-000001"

        # حساب المبلغ الإجمالي
        self.total_amount = self.subtotal + self.shipping_cost - self.discount
        super().save(*args, **kwargs)

    @property
    def can_be_cancelled(self):
        """فحص إمكانية إلغاء الطلب"""
        return self.status in ['new', 'confirmed']

    @property
    def is_completed(self):
        """فحص إذا كان الطلب مكتمل"""
        return self.status == 'delivered'


class OrderItem(models.Model):
    """نموذج عناصر الطلب"""
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='items', verbose_name='الطلب')
    product = models.ForeignKey(Product, on_delete=models.CASCADE, verbose_name='المنتج')
    quantity = models.PositiveIntegerField('الكمية', validators=[MinValueValidator(1)])
    unit_price = models.DecimalField('سعر الوحدة', max_digits=10, decimal_places=2)
    total_price = models.DecimalField('السعر الإجمالي', max_digits=10, decimal_places=2)

    class Meta:
        verbose_name = 'عنصر الطلب'
        verbose_name_plural = 'عناصر الطلب'
        unique_together = ['order', 'product']

    def __str__(self):
        return f"{self.product.name} x {self.quantity}"

    def save(self, *args, **kwargs):
        # حساب السعر الإجمالي
        self.total_price = self.quantity * self.unit_price
        super().save(*args, **kwargs)

        # تحديث المجموع الفرعي للطلب
        self.order.subtotal = sum(item.total_price for item in self.order.items.all())
        self.order.save()


class OrderStatusHistory(models.Model):
    """نموذج تاريخ حالات الطلب"""
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='status_history', verbose_name='الطلب')
    status = models.CharField('الحالة', max_length=20, choices=Order.STATUS_CHOICES)
    notes = models.TextField('ملاحظات', blank=True, null=True)
    created_at = models.DateTimeField('تاريخ التغيير', auto_now_add=True)

    class Meta:
        verbose_name = 'تاريخ حالة الطلب'
        verbose_name_plural = 'تاريخ حالات الطلبيات'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.order.order_number} - {self.get_status_display()}"
