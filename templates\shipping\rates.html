{% extends 'base.html' %}

{% block title %}أسعار الشحن - مدير التجارة الإلكترونية{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item"><a href="{% url 'shipping:list' %}">الشحن والتوصيل</a></li>
<li class="breadcrumb-item active">أسعار الشحن</li>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">إدارة أسعار الشحن</h1>
    <div>
        <a href="{% url 'shipping:rate_create' %}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>
            إضافة سعر جديد
        </a>
        <a href="{% url 'shipping:companies' %}" class="btn btn-outline-info">
            <i class="fas fa-building me-2"></i>
            شركات الشحن
        </a>
        <a href="{% url 'shipping:list' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للشحنات
        </a>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center border-primary">
            <div class="card-body">
                <h4 class="text-primary">{{ rates.count }}</h4>
                <p class="card-text">إجمالي الأسعار</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-success">
            <div class="card-body">
                <h4 class="text-success">{{ active_rates }}</h4>
                <p class="card-text">أسعار نشطة</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-info">
            <div class="card-body">
                <h4 class="text-info">{{ companies_count }}</h4>
                <p class="card-text">شركات الشحن</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-warning">
            <div class="card-body">
                <h4 class="text-warning">{{ zones_count }}</h4>
                <p class="card-text">مناطق الشحن</p>
            </div>
        </div>
    </div>
</div>

<!-- البحث والتصفية -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <select name="company" class="form-select">
                    <option value="">جميع الشركات</option>
                    {% for company in companies %}
                        <option value="{{ company.id }}" {% if company.id|stringformat:"s" == selected_company %}selected{% endif %}>{{ company.name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-4">
                <select name="zone" class="form-select">
                    <option value="">جميع المناطق</option>
                    {% for zone in zones %}
                        <option value="{{ zone.id }}" {% if zone.id|stringformat:"s" == selected_zone %}selected{% endif %}>{{ zone.name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <select name="status" class="form-select">
                    <option value="">جميع الحالات</option>
                    <option value="active" {% if selected_status == 'active' %}selected{% endif %}>نشط</option>
                    <option value="inactive" {% if selected_status == 'inactive' %}selected{% endif %}>غير نشط</option>
                </select>
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-outline-primary w-100">
                    <i class="fas fa-search"></i>
                    بحث
                </button>
            </div>
        </form>
    </div>
</div>

<!-- قائمة الأسعار -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-money-bill me-2"></i>
            قائمة أسعار الشحن
        </h5>
    </div>
    <div class="card-body">
        {% if rates %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>شركة الشحن</th>
                            <th>المنطقة</th>
                            <th>السعر الأساسي</th>
                            <th>سعر الكيلو</th>
                            <th>الشحن المجاني</th>
                            <th>مدة التوصيل</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for rate in rates %}
                        <tr>
                            <td>
                                <div>
                                    <strong>{{ rate.company.name }}</strong>
                                    {% if rate.company.phone %}
                                        <br><small class="text-muted">{{ rate.company.phone }}</small>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-info">{{ rate.zone.name }}</span>
                                <br><small class="text-muted">{{ rate.zone.get_zone_type_display }}</small>
                            </td>
                            <td>
                                <strong>{{ rate.base_price|floatformat:2 }} دج</strong>
                            </td>
                            <td>
                                {% if rate.price_per_kg %}
                                    {{ rate.price_per_kg|floatformat:2 }} دج/كيلو
                                {% else %}
                                    <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if rate.free_shipping_threshold %}
                                    {{ rate.free_shipping_threshold|floatformat:2 }} دج
                                {% else %}
                                    <span class="text-muted">غير متاح</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if rate.delivery_days_min and rate.delivery_days_max %}
                                    {{ rate.delivery_days_min }}-{{ rate.delivery_days_max }} يوم
                                {% elif rate.delivery_days_min %}
                                    {{ rate.delivery_days_min }} يوم
                                {% else %}
                                    <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if rate.is_active %}
                                    <span class="badge bg-success">نشط</span>
                                {% else %}
                                    <span class="badge bg-secondary">غير نشط</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{% url 'shipping:rate_update' rate.pk %}" class="btn btn-sm btn-outline-primary" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button class="btn btn-sm btn-outline-danger" onclick="confirmDelete({{ rate.pk }}, '{{ rate.company.name }} - {{ rate.zone.name }}')" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if is_paginated %}
                <nav aria-label="تصفح الصفحات">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if request.GET.company %}&company={{ request.GET.company }}{% endif %}{% if request.GET.zone %}&zone={{ request.GET.zone }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">الأولى</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.company %}&company={{ request.GET.company }}{% endif %}{% if request.GET.zone %}&zone={{ request.GET.zone }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">السابقة</a>
                            </li>
                        {% endif %}
                        
                        <li class="page-item active">
                            <span class="page-link">{{ page_obj.number }} من {{ page_obj.paginator.num_pages }}</span>
                        </li>
                        
                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.company %}&company={{ request.GET.company }}{% endif %}{% if request.GET.zone %}&zone={{ request.GET.zone }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">التالية</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.company %}&company={{ request.GET.company }}{% endif %}{% if request.GET.zone %}&zone={{ request.GET.zone }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">الأخيرة</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-money-bill fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد أسعار شحن</h4>
                <p class="text-muted">ابدأ بإضافة أسعار الشحن لشركات الشحن المختلفة</p>
                <a href="{% url 'shipping:rate_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    إضافة سعر جديد
                </a>
            </div>
        {% endif %}
    </div>
</div>

<!-- Modal تأكيد الحذف -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف سعر الشحن <strong id="rateName"></strong>؟</p>
                <p class="text-danger small">تحذير: سيتم حذف هذا السعر نهائياً.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <a href="#" id="deleteLink" class="btn btn-danger">حذف</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete(rateId, rateName) {
    document.getElementById('rateName').textContent = rateName;
    document.getElementById('deleteLink').href = '/shipping/rates/' + rateId + '/delete/';
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}
