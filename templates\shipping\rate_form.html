{% extends 'base.html' %}

{% block title %}إضافة سعر شحن جديد - مدير التجارة الإلكترونية{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item"><a href="{% url 'shipping:list' %}">الشحن والتوصيل</a></li>
<li class="breadcrumb-item"><a href="{% url 'shipping:rates' %}">أسعار الشحن</a></li>
<li class="breadcrumb-item active">إضافة سعر جديد</li>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-money-bill me-2"></i>
                    إضافة سعر شحن جديد
                </h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <!-- معلومات الشركة والمنطقة -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">الشركة والمنطقة</h6>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="{{ form.company.id_for_label }}" class="form-label">{{ form.company.label }}</label>
                                    {{ form.company }}
                                    {% if form.company.errors %}
                                        <div class="text-danger small">{{ form.company.errors.0 }}</div>
                                    {% endif %}
                                    {% if form.company.help_text %}
                                        <div class="form-text">{{ form.company.help_text }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6">
                                    <label for="{{ form.zone.id_for_label }}" class="form-label">{{ form.zone.label }}</label>
                                    {{ form.zone }}
                                    {% if form.zone.errors %}
                                        <div class="text-danger small">{{ form.zone.errors.0 }}</div>
                                    {% endif %}
                                    {% if form.zone.help_text %}
                                        <div class="form-text">{{ form.zone.help_text }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الأسعار -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">تفاصيل الأسعار</h6>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="{{ form.base_price.id_for_label }}" class="form-label">{{ form.base_price.label }}</label>
                                    <div class="input-group">
                                        {{ form.base_price }}
                                        <span class="input-group-text">دج</span>
                                    </div>
                                    {% if form.base_price.errors %}
                                        <div class="text-danger small">{{ form.base_price.errors.0 }}</div>
                                    {% endif %}
                                    {% if form.base_price.help_text %}
                                        <div class="form-text">{{ form.base_price.help_text }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6">
                                    <label for="{{ form.price_per_kg.id_for_label }}" class="form-label">{{ form.price_per_kg.label }}</label>
                                    <div class="input-group">
                                        {{ form.price_per_kg }}
                                        <span class="input-group-text">دج/كيلو</span>
                                    </div>
                                    {% if form.price_per_kg.errors %}
                                        <div class="text-danger small">{{ form.price_per_kg.errors.0 }}</div>
                                    {% endif %}
                                    {% if form.price_per_kg.help_text %}
                                        <div class="form-text">{{ form.price_per_kg.help_text }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-12">
                                    <label for="{{ form.free_shipping_threshold.id_for_label }}" class="form-label">{{ form.free_shipping_threshold.label }}</label>
                                    <div class="input-group">
                                        {{ form.free_shipping_threshold }}
                                        <span class="input-group-text">دج</span>
                                    </div>
                                    {% if form.free_shipping_threshold.errors %}
                                        <div class="text-danger small">{{ form.free_shipping_threshold.errors.0 }}</div>
                                    {% endif %}
                                    {% if form.free_shipping_threshold.help_text %}
                                        <div class="form-text">{{ form.free_shipping_threshold.help_text }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- مدة التوصيل -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">مدة التوصيل</h6>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="{{ form.delivery_days_min.id_for_label }}" class="form-label">{{ form.delivery_days_min.label }}</label>
                                    <div class="input-group">
                                        {{ form.delivery_days_min }}
                                        <span class="input-group-text">يوم</span>
                                    </div>
                                    {% if form.delivery_days_min.errors %}
                                        <div class="text-danger small">{{ form.delivery_days_min.errors.0 }}</div>
                                    {% endif %}
                                    {% if form.delivery_days_min.help_text %}
                                        <div class="form-text">{{ form.delivery_days_min.help_text }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6">
                                    <label for="{{ form.delivery_days_max.id_for_label }}" class="form-label">{{ form.delivery_days_max.label }}</label>
                                    <div class="input-group">
                                        {{ form.delivery_days_max }}
                                        <span class="input-group-text">يوم</span>
                                    </div>
                                    {% if form.delivery_days_max.errors %}
                                        <div class="text-danger small">{{ form.delivery_days_max.errors.0 }}</div>
                                    {% endif %}
                                    {% if form.delivery_days_max.help_text %}
                                        <div class="form-text">{{ form.delivery_days_max.help_text }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الحالة -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">إعدادات إضافية</h6>
                        </div>
                        <div class="card-body">
                            <div class="form-check form-switch">
                                {{ form.is_active }}
                                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                    {{ form.is_active.label }}
                                </label>
                                {% if form.is_active.errors %}
                                    <div class="text-danger small">{{ form.is_active.errors.0 }}</div>
                                {% endif %}
                                {% if form.is_active.help_text %}
                                    <div class="form-text">{{ form.is_active.help_text }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الحفظ والإلغاء -->
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ السعر
                        </button>
                        <a href="{% url 'shipping:rates' %}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- معلومات مساعدة -->
<div class="row justify-content-center mt-4">
    <div class="col-lg-8">
        <div class="card border-info">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات مساعدة
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>السعر الأساسي:</h6>
                        <p class="small text-muted">السعر الثابت للشحن بغض النظر عن الوزن</p>
                        
                        <h6>سعر الكيلو:</h6>
                        <p class="small text-muted">السعر الإضافي لكل كيلوغرام (اختياري)</p>
                    </div>
                    <div class="col-md-6">
                        <h6>حد الشحن المجاني:</h6>
                        <p class="small text-muted">المبلغ الذي يحصل عنده الزبون على شحن مجاني</p>
                        
                        <h6>مدة التوصيل:</h6>
                        <p class="small text-muted">الحد الأدنى والأقصى لمدة التوصيل بالأيام</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// التحقق من صحة مدة التوصيل
document.addEventListener('DOMContentLoaded', function() {
    const minDays = document.getElementById('{{ form.delivery_days_min.id_for_label }}');
    const maxDays = document.getElementById('{{ form.delivery_days_max.id_for_label }}');
    
    function validateDeliveryDays() {
        if (minDays.value && maxDays.value) {
            if (parseInt(minDays.value) > parseInt(maxDays.value)) {
                maxDays.setCustomValidity('أقصى مدة توصيل يجب أن تكون أكبر من أو تساوي أقل مدة توصيل');
            } else {
                maxDays.setCustomValidity('');
            }
        }
    }
    
    minDays.addEventListener('input', validateDeliveryDays);
    maxDays.addEventListener('input', validateDeliveryDays);
});
</script>
{% endblock %}
