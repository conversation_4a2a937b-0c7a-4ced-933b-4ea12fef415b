from django.db import models
from django.core.validators import MinValueValidator
from django.urls import reverse
from PIL import Image
import os


class Category(models.Model):
    """نموذج فئات المنتجات"""
    name = models.CharField('اسم الفئة', max_length=100, unique=True)
    description = models.TextField('وصف الفئة', blank=True, null=True)
    created_at = models.DateTimeField('تاريخ الإنشاء', auto_now_add=True)

    class Meta:
        verbose_name = 'فئة'
        verbose_name_plural = 'الفئات'
        ordering = ['name']

    def __str__(self):
        return self.name


class Product(models.Model):
    """نموذج المنتجات"""
    name = models.CharField('اسم المنتج', max_length=200)
    description = models.TextField('وصف المنتج', blank=True, null=True)
    category = models.ForeignKey(Category, on_delete=models.SET_NULL, null=True, blank=True, verbose_name='الفئة')
    price = models.DecimalField('السعر', max_digits=10, decimal_places=2, validators=[MinValueValidator(0)])
    cost_price = models.DecimalField('سعر التكلفة', max_digits=10, decimal_places=2, validators=[MinValueValidator(0)], blank=True, null=True)
    quantity = models.PositiveIntegerField('الكمية المتوفرة', default=0)
    min_quantity = models.PositiveIntegerField('الحد الأدنى للكمية', default=5, help_text='سيتم إرسال تنبيه عند الوصول لهذا الحد')
    image = models.ImageField('صورة المنتج', upload_to='products/', blank=True, null=True)
    sku = models.CharField('رمز المنتج', max_length=50, unique=True, blank=True, null=True)
    is_active = models.BooleanField('نشط', default=True)
    created_at = models.DateTimeField('تاريخ الإنشاء', auto_now_add=True)
    updated_at = models.DateTimeField('تاريخ التحديث', auto_now=True)

    class Meta:
        verbose_name = 'منتج'
        verbose_name_plural = 'المنتجات'
        ordering = ['-created_at']

    def __str__(self):
        return self.name

    def get_absolute_url(self):
        return reverse('products:detail', kwargs={'pk': self.pk})

    @property
    def profit_margin(self):
        """حساب هامش الربح"""
        if self.cost_price:
            return self.price - self.cost_price
        return 0

    @property
    def profit_percentage(self):
        """حساب نسبة الربح"""
        if self.cost_price and self.cost_price > 0:
            return ((self.price - self.cost_price) / self.cost_price) * 100
        return 0

    @property
    def is_low_stock(self):
        """فحص إذا كان المخزون منخفض"""
        return self.quantity <= self.min_quantity

    @property
    def is_out_of_stock(self):
        """فحص إذا كان المنتج نفد من المخزون"""
        return self.quantity == 0

    def save(self, *args, **kwargs):
        # تصغير حجم الصورة عند الحفظ
        super().save(*args, **kwargs)
        if self.image:
            img = Image.open(self.image.path)
            if img.height > 800 or img.width > 800:
                output_size = (800, 800)
                img.thumbnail(output_size)
                img.save(self.image.path)
