from django.db import models
from django.urls import reverse


class Customer(models.Model):
    """نموذج الزبائن"""
    GENDER_CHOICES = [
        ('M', 'ذكر'),
        ('F', 'أنثى'),
    ]

    first_name = models.Cha<PERSON><PERSON><PERSON>('الاسم الأول', max_length=50)
    last_name = models.Char<PERSON><PERSON>('اسم العائلة', max_length=50)
    phone = models.Char<PERSON>ield('رقم الهاتف', max_length=20, unique=True)
    email = models.Email<PERSON>ield('البريد الإلكتروني', blank=True, null=True)
    gender = models.Cha<PERSON><PERSON><PERSON>('الجنس', max_length=1, choices=GENDER_CHOICES, blank=True, null=True)
    birth_date = models.DateField('تاريخ الميلاد', blank=True, null=True)
    notes = models.TextField('ملاحظات', blank=True, null=True)
    is_active = models.<PERSON><PERSON>anField('نشط', default=True)
    created_at = models.DateTimeField('تاريخ الإنشاء', auto_now_add=True)
    updated_at = models.DateTimeField('تاريخ التحديث', auto_now=True)

    class Meta:
        verbose_name = 'زبون'
        verbose_name_plural = 'الزبائن'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.first_name} {self.last_name}"

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"

    def get_absolute_url(self):
        return reverse('customers:detail', kwargs={'pk': self.pk})

    @property
    def total_orders(self):
        """إجمالي عدد الطلبات"""
        return self.orders.count()

    @property
    def total_spent(self):
        """إجمالي المبلغ المنفق"""
        from orders.models import Order
        return self.orders.filter(status='delivered').aggregate(
            total=models.Sum('total_amount')
        )['total'] or 0


class Address(models.Model):
    """نموذج عناوين الزبائن"""
    WILAYA_CHOICES = [
        ('01', 'أدرار'), ('02', 'الشلف'), ('03', 'الأغواط'), ('04', 'أم البواقي'),
        ('05', 'باتنة'), ('06', 'بجاية'), ('07', 'بسكرة'), ('08', 'بشار'),
        ('09', 'البليدة'), ('10', 'البويرة'), ('11', 'تمنراست'), ('12', 'تبسة'),
        ('13', 'تلمسان'), ('14', 'تيارت'), ('15', 'تيزي وزو'), ('16', 'الجزائر'),
        ('17', 'الجلفة'), ('18', 'جيجل'), ('19', 'سطيف'), ('20', 'سعيدة'),
        ('21', 'سكيكدة'), ('22', 'سيدي بلعباس'), ('23', 'عنابة'), ('24', 'قالمة'),
        ('25', 'قسنطينة'), ('26', 'المدية'), ('27', 'مستغانم'), ('28', 'المسيلة'),
        ('29', 'معسكر'), ('30', 'ورقلة'), ('31', 'وهران'), ('32', 'البيض'),
        ('33', 'إليزي'), ('34', 'برج بوعريريج'), ('35', 'بومرداس'), ('36', 'الطارف'),
        ('37', 'تندوف'), ('38', 'تيسمسيلت'), ('39', 'الوادي'), ('40', 'خنشلة'),
        ('41', 'سوق أهراس'), ('42', 'تيبازة'), ('43', 'ميلة'), ('44', 'عين الدفلى'),
        ('45', 'النعامة'), ('46', 'عين تموشنت'), ('47', 'غرداية'), ('48', 'غليزان'),
        ('49', 'تيميمون'), ('50', 'برج باجي مختار'), ('51', 'أولاد جلال'),
        ('52', 'بني عباس'), ('53', 'عين صالح'), ('54', 'عين قزام'),
        ('55', 'تقرت'), ('56', 'جانت'), ('57', 'المغير'), ('58', 'المنيعة'),
    ]

    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, related_name='addresses', verbose_name='الزبون')
    street_address = models.CharField('العنوان', max_length=200)
    city = models.CharField('المدينة', max_length=100)
    wilaya = models.CharField('الولاية', max_length=2, choices=WILAYA_CHOICES)
    postal_code = models.CharField('الرمز البريدي', max_length=10, blank=True, null=True)
    is_default = models.BooleanField('العنوان الافتراضي', default=False)

    class Meta:
        verbose_name = 'عنوان'
        verbose_name_plural = 'العناوين'

    def __str__(self):
        return f"{self.street_address}, {self.city}, {self.get_wilaya_display()}"

    def save(self, *args, **kwargs):
        # إذا كان هذا العنوان افتراضي، قم بإلغاء الافتراضية من العناوين الأخرى
        if self.is_default:
            Address.objects.filter(customer=self.customer, is_default=True).update(is_default=False)
        super().save(*args, **kwargs)
