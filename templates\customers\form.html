{% extends 'base.html' %}

{% block title %}{{ title }} - مدير التجارة الإلكترونية{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item"><a href="{% url 'customers:list' %}">الزبائن</a></li>
<li class="breadcrumb-item active">{{ title }}</li>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user me-2"></i>
                    {{ title }}
                </h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <!-- المعلومات الشخصية -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">المعلومات الشخصية</h6>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="{{ form.first_name.id_for_label }}" class="form-label">{{ form.first_name.label }}</label>
                                    {{ form.first_name }}
                                    {% if form.first_name.errors %}
                                        <div class="text-danger small">{{ form.first_name.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6">
                                    <label for="{{ form.last_name.id_for_label }}" class="form-label">{{ form.last_name.label }}</label>
                                    {{ form.last_name }}
                                    {% if form.last_name.errors %}
                                        <div class="text-danger small">{{ form.last_name.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6">
                                    <label for="{{ form.phone.id_for_label }}" class="form-label">{{ form.phone.label }}</label>
                                    {{ form.phone }}
                                    {% if form.phone.errors %}
                                        <div class="text-danger small">{{ form.phone.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6">
                                    <label for="{{ form.email.id_for_label }}" class="form-label">{{ form.email.label }}</label>
                                    {{ form.email }}
                                    {% if form.email.errors %}
                                        <div class="text-danger small">{{ form.email.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6">
                                    <label for="{{ form.gender.id_for_label }}" class="form-label">{{ form.gender.label }}</label>
                                    {{ form.gender }}
                                    {% if form.gender.errors %}
                                        <div class="text-danger small">{{ form.gender.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6">
                                    <label for="{{ form.birth_date.id_for_label }}" class="form-label">{{ form.birth_date.label }}</label>
                                    {{ form.birth_date }}
                                    {% if form.birth_date.errors %}
                                        <div class="text-danger small">{{ form.birth_date.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات إضافية -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">معلومات إضافية</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="{{ form.notes.id_for_label }}" class="form-label">{{ form.notes.label }}</label>
                                {{ form.notes }}
                                {% if form.notes.errors %}
                                    <div class="text-danger small">{{ form.notes.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="form-check form-switch">
                                {{ form.is_active }}
                                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                    {{ form.is_active.label }}
                                </label>
                                {% if form.is_active.errors %}
                                    <div class="text-danger small">{{ form.is_active.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الحفظ والإلغاء -->
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ الزبون
                        </button>
                        <a href="{% url 'customers:list' %}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                        {% if object %}
                            <a href="{% url 'customers:detail' object.pk %}" class="btn btn-outline-info">
                                <i class="fas fa-eye me-2"></i>
                                عرض الزبون
                            </a>
                        {% endif %}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
