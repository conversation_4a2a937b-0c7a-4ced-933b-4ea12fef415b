from django.core.management.base import BaseCommand
from products.models import Category, Product
from customers.models import Customer
from settings_app.models import StoreSettings


class Command(BaseCommand):
    help = 'إنشاء بيانات تجريبية للنظام'

    def handle(self, *args, **options):
        self.stdout.write('بدء إنشاء البيانات التجريبية...')
        
        # إنشاء إعدادات المتجر
        store_settings, created = StoreSettings.objects.get_or_create(
            pk=1,
            defaults={
                'store_name': 'متجر الإلكترونيات',
                'store_description': 'متجر متخصص في بيع الإلكترونيات والأجهزة الذكية',
                'phone': '0555123456',
                'email': '<EMAIL>',
                'address': 'الرياض، المملكة العربية السعودية',
                'currency': 'DZD',
                'default_shipping_cost': 50.00,
                'free_shipping_threshold': 500.00,
                'low_stock_threshold': 5,
            }
        )
        if created:
            self.stdout.write('تم إنشاء إعدادات المتجر')
        
        # إنشاء الفئات
        categories_data = [
            {'name': 'الهواتف الذكية', 'description': 'أحدث الهواتف الذكية'},
            {'name': 'أجهزة الكمبيوتر', 'description': 'أجهزة كمبيوتر محمولة ومكتبية'},
            {'name': 'الإكسسوارات', 'description': 'إكسسوارات الهواتف والأجهزة'},
            {'name': 'الأجهزة المنزلية', 'description': 'أجهزة كهربائية منزلية'},
        ]
        
        for cat_data in categories_data:
            category, created = Category.objects.get_or_create(
                name=cat_data['name'],
                defaults={'description': cat_data['description']}
            )
            if created:
                self.stdout.write(f'تم إنشاء فئة: {category.name}')
        
        # إنشاء المنتجات
        products_data = [
            {
                'name': 'iPhone 15 Pro',
                'description': 'أحدث هاتف من آبل مع كاميرا متطورة',
                'category': 'الهواتف الذكية',
                'price': 4500.00,
                'cost_price': 3800.00,
                'quantity': 25,
                'min_quantity': 5,
                'sku': 'IP15PRO'
            },
            {
                'name': 'Samsung Galaxy S24',
                'description': 'هاتف سامسونج الرائد مع شاشة AMOLED',
                'category': 'الهواتف الذكية',
                'price': 3800.00,
                'cost_price': 3200.00,
                'quantity': 30,
                'min_quantity': 5,
                'sku': 'SGS24'
            },
            {
                'name': 'MacBook Air M3',
                'description': 'لابتوب آبل بمعالج M3 الجديد',
                'category': 'أجهزة الكمبيوتر',
                'price': 8500.00,
                'cost_price': 7200.00,
                'quantity': 15,
                'min_quantity': 3,
                'sku': 'MBAM3'
            },
            {
                'name': 'Dell XPS 13',
                'description': 'لابتوب ديل عالي الأداء',
                'category': 'أجهزة الكمبيوتر',
                'price': 6200.00,
                'cost_price': 5400.00,
                'quantity': 12,
                'min_quantity': 3,
                'sku': 'DXPS13'
            },
            {
                'name': 'AirPods Pro',
                'description': 'سماعات آبل اللاسلكية مع إلغاء الضوضاء',
                'category': 'الإكسسوارات',
                'price': 1200.00,
                'cost_price': 950.00,
                'quantity': 50,
                'min_quantity': 10,
                'sku': 'APPRO'
            },
            {
                'name': 'شاحن لاسلكي سريع',
                'description': 'شاحن لاسلكي بقوة 15 واط',
                'category': 'الإكسسوارات',
                'price': 180.00,
                'cost_price': 120.00,
                'quantity': 3,  # مخزون منخفض
                'min_quantity': 5,
                'sku': 'WC15W'
            },
            {
                'name': 'مكيف هواء سبليت',
                'description': 'مكيف هواء 18000 وحدة حرارية',
                'category': 'الأجهزة المنزلية',
                'price': 2800.00,
                'cost_price': 2200.00,
                'quantity': 8,
                'min_quantity': 2,
                'sku': 'AC18K'
            },
            {
                'name': 'ثلاجة نوفروست',
                'description': 'ثلاجة بتقنية نوفروست 350 لتر',
                'category': 'الأجهزة المنزلية',
                'price': 3500.00,
                'cost_price': 2900.00,
                'quantity': 0,  # نفد من المخزون
                'min_quantity': 2,
                'sku': 'RF350NF'
            },
        ]
        
        for prod_data in products_data:
            category = Category.objects.get(name=prod_data['category'])
            product, created = Product.objects.get_or_create(
                sku=prod_data['sku'],
                defaults={
                    'name': prod_data['name'],
                    'description': prod_data['description'],
                    'category': category,
                    'price': prod_data['price'],
                    'cost_price': prod_data['cost_price'],
                    'quantity': prod_data['quantity'],
                    'min_quantity': prod_data['min_quantity'],
                }
            )
            if created:
                self.stdout.write(f'تم إنشاء منتج: {product.name}')
        
        # إنشاء زبائن تجريبيين
        customers_data = [
            {
                'first_name': 'أحمد',
                'last_name': 'محمد',
                'phone': '0551234567',
                'email': '<EMAIL>',
                'gender': 'male'
            },
            {
                'first_name': 'فاطمة',
                'last_name': 'علي',
                'phone': '0552345678',
                'email': '<EMAIL>',
                'gender': 'female'
            },
            {
                'first_name': 'خالد',
                'last_name': 'السعد',
                'phone': '0553456789',
                'email': '<EMAIL>',
                'gender': 'male'
            },
            {
                'first_name': 'نورا',
                'last_name': 'الأحمد',
                'phone': '0554567890',
                'email': '<EMAIL>',
                'gender': 'female'
            },
        ]
        
        for cust_data in customers_data:
            customer, created = Customer.objects.get_or_create(
                phone=cust_data['phone'],
                defaults=cust_data
            )
            if created:
                self.stdout.write(f'تم إنشاء زبون: {customer.full_name}')
        
        self.stdout.write(
            self.style.SUCCESS('تم إنشاء البيانات التجريبية بنجاح!')
        )
