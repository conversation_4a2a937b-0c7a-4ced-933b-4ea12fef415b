from django.shortcuts import render, get_object_or_404, redirect
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.contrib import messages
from django.urls import reverse_lazy
from django.db.models import Q, F
from django.http import JsonResponse

from .models import Product, Category
from .forms import ProductForm, CategoryForm


class ProductListView(ListView):
    """عرض قائمة المنتجات"""
    model = Product
    template_name = 'products/list.html'
    context_object_name = 'products'
    paginate_by = 20

    def get_queryset(self):
        queryset = Product.objects.select_related('category').order_by('-created_at')

        # البحث
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(description__icontains=search) |
                Q(sku__icontains=search)
            )

        # تصفية حسب الفئة
        category = self.request.GET.get('category')
        if category:
            queryset = queryset.filter(category_id=category)

        # تصفية حسب حالة المخزون
        stock_status = self.request.GET.get('stock_status')
        if stock_status == 'low':
            queryset = queryset.filter(quantity__lte=F('min_quantity'))
        elif stock_status == 'out':
            queryset = queryset.filter(quantity=0)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['categories'] = Category.objects.all()
        context['search'] = self.request.GET.get('search', '')
        context['selected_category'] = self.request.GET.get('category', '')
        context['selected_stock_status'] = self.request.GET.get('stock_status', '')
        return context


class ProductDetailView(DetailView):
    """عرض تفاصيل المنتج"""
    model = Product
    template_name = 'products/detail.html'
    context_object_name = 'product'


class ProductCreateView(CreateView):
    """إضافة منتج جديد"""
    model = Product
    form_class = ProductForm
    template_name = 'products/form.html'
    success_url = reverse_lazy('products:list')

    def form_valid(self, form):
        messages.success(self.request, 'تم إضافة المنتج بنجاح')
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'إضافة منتج جديد'
        return context


class ProductUpdateView(UpdateView):
    """تعديل المنتج"""
    model = Product
    form_class = ProductForm
    template_name = 'products/form.html'
    success_url = reverse_lazy('products:list')

    def form_valid(self, form):
        messages.success(self.request, 'تم تحديث المنتج بنجاح')
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = f'تعديل المنتج: {self.object.name}'
        return context


class ProductDeleteView(DeleteView):
    """حذف المنتج"""
    model = Product
    template_name = 'products/delete.html'
    success_url = reverse_lazy('products:list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'تم حذف المنتج بنجاح')
        return super().delete(request, *args, **kwargs)


class CategoryListView(ListView):
    """عرض قائمة الفئات"""
    model = Category
    template_name = 'products/categories.html'
    context_object_name = 'categories'


class CategoryCreateView(CreateView):
    """إضافة فئة جديدة"""
    model = Category
    form_class = CategoryForm
    template_name = 'products/category_form.html'
    success_url = reverse_lazy('products:categories')

    def form_valid(self, form):
        messages.success(self.request, 'تم إضافة الفئة بنجاح')
        return super().form_valid(form)


class LowStockView(ListView):
    """عرض المنتجات منخفضة المخزون"""
    model = Product
    template_name = 'products/low_stock.html'
    context_object_name = 'products'

    def get_queryset(self):
        return Product.objects.filter(
            quantity__lte=F('min_quantity')
        ).order_by('quantity')
