{% extends 'base.html' %}

{% block title %}إعدادات المتجر - مدير التجارة الإلكترونية{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item"><a href="{% url 'settings:index' %}">الإعدادات</a></li>
<li class="breadcrumb-item active">إعدادات المتجر</li>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-10">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-store me-2"></i>
                    إعدادات المتجر
                </h5>
            </div>
            <div class="card-body">
                <form method="post" enctype="multipart/form-data">
                    {% csrf_token %}
                    
                    <!-- معلومات المتجر الأساسية -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">معلومات المتجر</h6>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-12">
                                    <label for="{{ form.store_name.id_for_label }}" class="form-label">{{ form.store_name.label }}</label>
                                    {{ form.store_name }}
                                    {% if form.store_name.errors %}
                                        <div class="text-danger small">{{ form.store_name.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-12">
                                    <label for="{{ form.store_description.id_for_label }}" class="form-label">{{ form.store_description.label }}</label>
                                    {{ form.store_description }}
                                    {% if form.store_description.errors %}
                                        <div class="text-danger small">{{ form.store_description.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-12">
                                    <label for="{{ form.store_logo.id_for_label }}" class="form-label">{{ form.store_logo.label }}</label>
                                    {{ form.store_logo }}
                                    {% if form.store_logo.errors %}
                                        <div class="text-danger small">{{ form.store_logo.errors.0 }}</div>
                                    {% endif %}
                                    {% if object and object.store_logo %}
                                        <div class="mt-2">
                                            <img src="{{ object.store_logo.url }}" alt="شعار المتجر" class="img-thumbnail" style="max-height: 100px;">
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات الاتصال -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">معلومات الاتصال</h6>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="{{ form.phone.id_for_label }}" class="form-label">{{ form.phone.label }}</label>
                                    {{ form.phone }}
                                    {% if form.phone.errors %}
                                        <div class="text-danger small">{{ form.phone.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6">
                                    <label for="{{ form.email.id_for_label }}" class="form-label">{{ form.email.label }}</label>
                                    {{ form.email }}
                                    {% if form.email.errors %}
                                        <div class="text-danger small">{{ form.email.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-12">
                                    <label for="{{ form.address.id_for_label }}" class="form-label">{{ form.address.label }}</label>
                                    {{ form.address }}
                                    {% if form.address.errors %}
                                        <div class="text-danger small">{{ form.address.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-12">
                                    <label for="{{ form.website.id_for_label }}" class="form-label">{{ form.website.label }}</label>
                                    {{ form.website }}
                                    {% if form.website.errors %}
                                        <div class="text-danger small">{{ form.website.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الإعدادات المالية -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">العملة والأسعار</h6>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="{{ form.currency.id_for_label }}" class="form-label">{{ form.currency.label }}</label>
                                    {{ form.currency }}
                                    {% if form.currency.errors %}
                                        <div class="text-danger small">{{ form.currency.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6">
                                    <label for="{{ form.tax_rate.id_for_label }}" class="form-label">{{ form.tax_rate.label }}</label>
                                    <div class="input-group">
                                        {{ form.tax_rate }}
                                        <span class="input-group-text">%</span>
                                    </div>
                                    {% if form.tax_rate.errors %}
                                        <div class="text-danger small">{{ form.tax_rate.errors.0 }}</div>
                                    {% endif %}
                                    {% if form.tax_rate.help_text %}
                                        <div class="form-text">{{ form.tax_rate.help_text }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6">
                                    <label for="{{ form.default_shipping_cost.id_for_label }}" class="form-label">{{ form.default_shipping_cost.label }}</label>
                                    <div class="input-group">
                                        {{ form.default_shipping_cost }}
                                        <span class="input-group-text">دج</span>
                                    </div>
                                    {% if form.default_shipping_cost.errors %}
                                        <div class="text-danger small">{{ form.default_shipping_cost.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6">
                                    <label for="{{ form.free_shipping_threshold.id_for_label }}" class="form-label">{{ form.free_shipping_threshold.label }}</label>
                                    <div class="input-group">
                                        {{ form.free_shipping_threshold }}
                                        <span class="input-group-text">دج</span>
                                    </div>
                                    {% if form.free_shipping_threshold.errors %}
                                        <div class="text-danger small">{{ form.free_shipping_threshold.errors.0 }}</div>
                                    {% endif %}
                                    {% if form.free_shipping_threshold.help_text %}
                                        <div class="form-text">{{ form.free_shipping_threshold.help_text }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- إعدادات المخزون -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">إعدادات المخزون والتنبيهات</h6>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="{{ form.low_stock_threshold.id_for_label }}" class="form-label">{{ form.low_stock_threshold.label }}</label>
                                    <div class="input-group">
                                        {{ form.low_stock_threshold }}
                                        <span class="input-group-text">قطعة</span>
                                    </div>
                                    {% if form.low_stock_threshold.errors %}
                                        <div class="text-danger small">{{ form.low_stock_threshold.errors.0 }}</div>
                                    {% endif %}
                                    {% if form.low_stock_threshold.help_text %}
                                        <div class="form-text">{{ form.low_stock_threshold.help_text }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6">
                                    <div class="mt-4">
                                        <div class="form-check form-switch mb-3">
                                            {{ form.enable_low_stock_alerts }}
                                            <label class="form-check-label" for="{{ form.enable_low_stock_alerts.id_for_label }}">
                                                {{ form.enable_low_stock_alerts.label }}
                                            </label>
                                        </div>
                                        <div class="form-check form-switch">
                                            {{ form.enable_new_order_alerts }}
                                            <label class="form-check-label" for="{{ form.enable_new_order_alerts.id_for_label }}">
                                                {{ form.enable_new_order_alerts.label }}
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- إعدادات الفواتير -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">إعدادات الفواتير</h6>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="{{ form.invoice_prefix.id_for_label }}" class="form-label">{{ form.invoice_prefix.label }}</label>
                                    {{ form.invoice_prefix }}
                                    {% if form.invoice_prefix.errors %}
                                        <div class="text-danger small">{{ form.invoice_prefix.errors.0 }}</div>
                                    {% endif %}
                                    {% if form.invoice_prefix.help_text %}
                                        <div class="form-text">{{ form.invoice_prefix.help_text }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-12">
                                    <label for="{{ form.invoice_footer.id_for_label }}" class="form-label">{{ form.invoice_footer.label }}</label>
                                    {{ form.invoice_footer }}
                                    {% if form.invoice_footer.errors %}
                                        <div class="text-danger small">{{ form.invoice_footer.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الحفظ والإلغاء -->
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-save me-2"></i>
                            حفظ الإعدادات
                        </button>
                        <a href="{% url 'settings:index' %}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
