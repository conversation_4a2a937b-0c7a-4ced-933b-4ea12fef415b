{% extends 'base.html' %}

{% block title %}الزبائن - مدير التجارة الإلكترونية{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item active">الزبائن</li>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">إدارة الزبائن</h1>
    <div>
        <a href="{% url 'customers:create' %}" class="btn btn-primary">
            <i class="fas fa-user-plus me-2"></i>
            إضافة زبون جديد
        </a>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center border-primary">
            <div class="card-body">
                <h4 class="text-primary">{{ total_customers }}</h4>
                <p class="card-text">إجمالي الزبائن</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-success">
            <div class="card-body">
                <h4 class="text-success">{{ active_customers }}</h4>
                <p class="card-text">زبائن نشطين</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-info">
            <div class="card-body">
                <h4 class="text-info">{{ new_customers_this_month }}</h4>
                <p class="card-text">زبائن جدد هذا الشهر</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-warning">
            <div class="card-body">
                <h4 class="text-warning">
                    {% if total_customers > 0 %}
                        {% widthratio active_customers total_customers 100 %}%
                    {% else %}
                        0%
                    {% endif %}
                </h4>
                <p class="card-text">معدل النشاط</p>
            </div>
        </div>
    </div>
</div>

<!-- البحث والتصفية -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-6">
                <input type="text" name="search" class="form-control" placeholder="البحث في الزبائن..." value="{{ search }}">
            </div>
            <div class="col-md-3">
                <select name="status" class="form-select">
                    <option value="">جميع الزبائن</option>
                    <option value="active" {% if selected_status == 'active' %}selected{% endif %}>نشط</option>
                    <option value="inactive" {% if selected_status == 'inactive' %}selected{% endif %}>غير نشط</option>
                </select>
            </div>
            <div class="col-md-2">
                <select name="gender" class="form-select">
                    <option value="">جميع الأجناس</option>
                    {% for value, label in gender_choices %}
                        <option value="{{ value }}" {% if value == selected_gender %}selected{% endif %}>{{ label }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-1">
                <button type="submit" class="btn btn-outline-primary w-100">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </form>
    </div>
</div>

<!-- قائمة الزبائن -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-users me-2"></i>
            قائمة الزبائن
        </h5>
    </div>
    <div class="card-body">
        {% if customers %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>الاسم</th>
                            <th>رقم الهاتف</th>
                            <th>البريد الإلكتروني</th>
                            <th>عدد الطلبيات</th>
                            <th>إجمالي المشتريات</th>
                            <th>الحالة</th>
                            <th>تاريخ التسجيل</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for customer in customers %}
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar-circle me-3">
                                        {{ customer.first_name.0 }}{{ customer.last_name.0 }}
                                    </div>
                                    <div>
                                        <strong>{{ customer.full_name }}</strong>
                                        {% if customer.gender %}
                                            <br><small class="text-muted">{{ customer.get_gender_display }}</small>
                                        {% endif %}
                                    </div>
                                </div>
                            </td>
                            <td>
                                <a href="tel:{{ customer.phone }}" class="text-decoration-none">
                                    {{ customer.phone }}
                                </a>
                            </td>
                            <td>
                                {% if customer.email %}
                                    <a href="mailto:{{ customer.email }}" class="text-decoration-none">
                                        {{ customer.email }}
                                    </a>
                                {% else %}
                                    <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-info">{{ customer.orders_count|default:0 }}</span>
                            </td>
                            <td>
                                {% if customer.spent_amount %}
                                    <strong>{{ customer.spent_amount|floatformat:2 }} دج</strong>
                                {% else %}
                                    <span class="text-muted">0.00 دج</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if customer.is_active %}
                                    <span class="badge bg-success">نشط</span>
                                {% else %}
                                    <span class="badge bg-secondary">غير نشط</span>
                                {% endif %}
                            </td>
                            <td>{{ customer.created_at|date:"d/m/Y" }}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{% url 'customers:detail' customer.pk %}" class="btn btn-sm btn-outline-info" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'customers:edit' customer.pk %}" class="btn btn-sm btn-outline-primary" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'customers:orders' customer.pk %}" class="btn btn-sm btn-outline-success" title="الطلبيات">
                                        <i class="fas fa-shopping-cart"></i>
                                    </a>
                                    <a href="{% url 'orders:create' %}?customer={{ customer.pk }}" class="btn btn-sm btn-outline-warning" title="طلب جديد">
                                        <i class="fas fa-plus"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if is_paginated %}
                <nav aria-label="تصفح الصفحات">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.gender %}&gender={{ request.GET.gender }}{% endif %}">الأولى</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.gender %}&gender={{ request.GET.gender }}{% endif %}">السابقة</a>
                            </li>
                        {% endif %}
                        
                        <li class="page-item active">
                            <span class="page-link">{{ page_obj.number }} من {{ page_obj.paginator.num_pages }}</span>
                        </li>
                        
                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.gender %}&gender={{ request.GET.gender }}{% endif %}">التالية</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.gender %}&gender={{ request.GET.gender }}{% endif %}">الأخيرة</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-users fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">لا يوجد زبائن</h4>
                <p class="text-muted">ابدأ بإضافة زبونك الأول</p>
                <a href="{% url 'customers:create' %}" class="btn btn-primary">
                    <i class="fas fa-user-plus me-2"></i>
                    إضافة زبون جديد
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
}
</style>
{% endblock %}
