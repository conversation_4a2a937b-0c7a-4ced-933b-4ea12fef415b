from django.contrib import admin
from .models import Category, Product


@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'created_at']
    search_fields = ['name']
    ordering = ['name']


@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    list_display = ['name', 'category', 'price', 'quantity', 'is_low_stock', 'is_active', 'created_at']
    list_filter = ['category', 'is_active', 'created_at']
    search_fields = ['name', 'sku', 'description']
    list_editable = ['price', 'quantity', 'is_active']
    ordering = ['-created_at']

    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('name', 'description', 'category', 'sku', 'image')
        }),
        ('الأسعار والكميات', {
            'fields': ('price', 'cost_price', 'quantity', 'min_quantity')
        }),
        ('الحالة', {
            'fields': ('is_active',)
        }),
    )

    def is_low_stock(self, obj):
        return obj.is_low_stock
    is_low_stock.boolean = True
    is_low_stock.short_description = 'مخزون منخفض'
