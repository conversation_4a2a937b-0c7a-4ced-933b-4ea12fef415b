from django import forms
from django.forms import inlineformset_factory

from .models import Order, OrderItem
from customers.models import Customer
from products.models import Product


class OrderForm(forms.ModelForm):
    """نموذج إضافة/تعديل الطلب"""
    
    class Meta:
        model = Order
        fields = [
            'customer', 'status', 'payment_status', 'shipping_address',
            'shipping_city', 'shipping_wilaya', 'shipping_phone',
            'shipping_cost', 'discount', 'notes'
        ]
        widgets = {
            'shipping_address': forms.Textarea(attrs={'rows': 3, 'placeholder': 'العنوان الكامل للتوصيل'}),
            'shipping_city': forms.TextInput(attrs={'placeholder': 'المدينة'}),
            'shipping_wilaya': forms.TextInput(attrs={'placeholder': 'الولاية'}),
            'shipping_phone': forms.TextInput(attrs={'placeholder': 'رقم الهاتف للتوصيل'}),
            'shipping_cost': forms.NumberInput(attrs={'step': '0.01', 'min': '0'}),
            'discount': forms.NumberInput(attrs={'step': '0.01', 'min': '0'}),
            'notes': forms.Textarea(attrs={'rows': 3, 'placeholder': 'ملاحظات إضافية'}),
        }
        labels = {
            'customer': 'الزبون',
            'status': 'حالة الطلب',
            'payment_status': 'حالة الدفع',
            'shipping_address': 'عنوان التوصيل',
            'shipping_city': 'مدينة التوصيل',
            'shipping_wilaya': 'ولاية التوصيل',
            'shipping_phone': 'هاتف التوصيل',
            'shipping_cost': 'تكلفة الشحن (دج)',
            'discount': 'الخصم (دج)',
            'notes': 'ملاحظات',
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # تحسين عرض قائمة الزبائن
        self.fields['customer'].queryset = Customer.objects.filter(is_active=True).order_by('first_name', 'last_name')
        self.fields['customer'].empty_label = 'اختر الزبون'

        # إضافة CSS classes للحقول
        for field_name, field in self.fields.items():
            if field_name in ['shipping_address', 'notes']:
                field.widget.attrs['class'] = 'form-control'
            else:
                field.widget.attrs['class'] = 'form-control'


class OrderItemForm(forms.ModelForm):
    """نموذج عنصر الطلب"""
    
    class Meta:
        model = OrderItem
        fields = ['product', 'quantity', 'unit_price']
        widgets = {
            'quantity': forms.NumberInput(attrs={'min': '1', 'class': 'form-control'}),
            'unit_price': forms.NumberInput(attrs={'step': '0.01', 'min': '0', 'class': 'form-control'}),
        }
        labels = {
            'product': 'المنتج',
            'quantity': 'الكمية',
            'unit_price': 'سعر الوحدة (دج)',
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['product'].queryset = Product.objects.filter(is_active=True).order_by('name')
        self.fields['product'].empty_label = 'اختر المنتج'

        # إضافة CSS classes
        for field_name, field in self.fields.items():
            field.widget.attrs['class'] = 'form-control'

        # إذا تم اختيار منتج، ضع سعره كسعر افتراضي
        if self.instance and self.instance.pk and hasattr(self.instance, 'product') and self.instance.product:
            self.fields['unit_price'].initial = self.instance.product.price


# إنشاء FormSet لعناصر الطلب
OrderItemFormSet = inlineformset_factory(
    Order, OrderItem,
    form=OrderItemForm,
    extra=1,
    min_num=0,
    validate_min=False,
    can_delete=True
)


class OrderStatusForm(forms.ModelForm):
    """نموذج تحديث حالة الطلب"""
    notes = forms.CharField(
        label='ملاحظات',
        widget=forms.Textarea(attrs={'rows': 3, 'placeholder': 'ملاحظات حول تغيير الحالة'}),
        required=False
    )
    
    class Meta:
        model = Order
        fields = ['status']
        labels = {
            'status': 'الحالة الجديدة',
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # إضافة CSS classes
        for field_name, field in self.fields.items():
            if field_name == 'notes':
                field.widget.attrs['class'] = 'form-control'
            else:
                field.widget.attrs['class'] = 'form-control'


class OrderSearchForm(forms.Form):
    """نموذج البحث في الطلبيات"""
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'placeholder': 'البحث برقم الطلب أو اسم الزبون...',
            'class': 'form-control'
        })
    )
    status = forms.ChoiceField(
        choices=[('', 'جميع الحالات')] + Order.STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    payment_status = forms.ChoiceField(
        choices=[('', 'جميع حالات الدفع')] + Order.PAYMENT_STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'})
    )
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'})
    )


class QuickOrderForm(forms.Form):
    """نموذج إضافة طلب سريع"""
    customer = forms.ModelChoiceField(
        queryset=Customer.objects.filter(is_active=True),
        label='الزبون',
        empty_label='اختر الزبون',
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    product = forms.ModelChoiceField(
        queryset=Product.objects.filter(is_active=True),
        label='المنتج',
        empty_label='اختر المنتج',
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    quantity = forms.IntegerField(
        label='الكمية',
        min_value=1,
        initial=1,
        widget=forms.NumberInput(attrs={'class': 'form-control'})
    )
    
    def __init__(self, *args, **kwargs):
        # إزالة instance إذا كان موجوداً (لأن forms.Form لا يقبله)
        kwargs.pop('instance', None)
        super().__init__(*args, **kwargs)
        # إضافة CSS classes
        for field_name, field in self.fields.items():
            field.widget.attrs['class'] = 'form-control'

    def save(self):
        """إنشاء طلب جديد من البيانات"""
        customer = self.cleaned_data['customer']
        product = self.cleaned_data['product']
        quantity = self.cleaned_data['quantity']

        # إنشاء الطلب
        order = Order.objects.create(
            customer=customer,
            shipping_address=customer.addresses.filter(is_default=True).first().street_address if customer.addresses.filter(is_default=True).exists() else 'عنوان غير محدد',
            shipping_city=customer.addresses.filter(is_default=True).first().city if customer.addresses.filter(is_default=True).exists() else 'مدينة غير محددة',
            shipping_wilaya=customer.addresses.filter(is_default=True).first().wilaya if customer.addresses.filter(is_default=True).exists() else 'ولاية غير محددة',
            shipping_phone=customer.phone,
            status='new',
            payment_status='pending'
        )

        # إنشاء عنصر الطلب
        OrderItem.objects.create(
            order=order,
            product=product,
            quantity=quantity,
            unit_price=product.price
        )

        # إنشاء سجل في تاريخ الحالات
        from .models import OrderStatusHistory
        OrderStatusHistory.objects.create(
            order=order,
            status=order.status,
            notes='تم إنشاء الطلب'
        )

        return order
