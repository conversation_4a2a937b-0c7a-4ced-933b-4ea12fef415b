from django.contrib import admin
from .models import StoreSettings, OrderStatus, PaymentMethod


@admin.register(StoreSettings)
class StoreSettingsAdmin(admin.ModelAdmin):
    list_display = ['store_name', 'currency', 'updated_at']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('معلومات المتجر', {
            'fields': ('store_name', 'store_description', 'store_logo')
        }),
        ('معلومات الاتصال', {
            'fields': ('phone', 'email', 'address', 'website')
        }),
        ('الإعدادات المالية', {
            'fields': ('currency', 'tax_rate', 'default_shipping_cost', 'free_shipping_threshold')
        }),
        ('إعدادات المخزون', {
            'fields': ('low_stock_threshold', 'enable_low_stock_alerts', 'enable_new_order_alerts')
        }),
        ('إعدادات الفواتير', {
            'fields': ('invoice_prefix', 'invoice_footer')
        }),
        ('تواريخ', {
            'fields': ('created_at', 'updated_at')
        }),
    )


@admin.register(OrderStatus)
class OrderStatusAdmin(admin.ModelAdmin):
    list_display = ['display_name', 'name', 'color', 'order', 'is_active']
    list_filter = ['is_active']
    search_fields = ['name', 'display_name']
    list_editable = ['order', 'is_active']
    ordering = ['order', 'name']


@admin.register(PaymentMethod)
class PaymentMethodAdmin(admin.ModelAdmin):
    list_display = ['name', 'requires_confirmation', 'is_active']
    list_filter = ['requires_confirmation', 'is_active']
    search_fields = ['name']
    list_editable = ['requires_confirmation', 'is_active']
