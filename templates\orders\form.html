{% extends 'base.html' %}

{% block title %}{{ title }} - مدير التجارة الإلكترونية{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item"><a href="{% url 'orders:list' %}">الطلبيات</a></li>
<li class="breadcrumb-item active">{{ title }}</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-shopping-cart me-2"></i>
                    {{ title }}
                </h5>
            </div>
            <div class="card-body">
                <form method="post" id="orderForm">
                    {% csrf_token %}
                    
                    <!-- معلومات الطلب الأساسية -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">معلومات الطلب الأساسية</h6>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="{{ form.customer.id_for_label }}" class="form-label">{{ form.customer.label }}</label>
                                    {{ form.customer }}
                                    {% if form.customer.errors %}
                                        <div class="text-danger small">{{ form.customer.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-3">
                                    <label for="{{ form.status.id_for_label }}" class="form-label">{{ form.status.label }}</label>
                                    {{ form.status }}
                                    {% if form.status.errors %}
                                        <div class="text-danger small">{{ form.status.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-3">
                                    <label for="{{ form.payment_status.id_for_label }}" class="form-label">{{ form.payment_status.label }}</label>
                                    {{ form.payment_status }}
                                    {% if form.payment_status.errors %}
                                        <div class="text-danger small">{{ form.payment_status.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- عناصر الطلب -->
                    <div class="card mb-4">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">عناصر الطلب</h6>
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="addOrderItem()">
                                <i class="fas fa-plus me-1"></i>
                                إضافة منتج
                            </button>
                        </div>
                        <div class="card-body">
                            {{ formset.management_form }}
                            <div id="order-items">
                                {% for form in formset %}
                                    <div class="order-item-form border rounded p-3 mb-3">
                                        {% if form.non_field_errors %}
                                            <div class="alert alert-danger">{{ form.non_field_errors }}</div>
                                        {% endif %}
                                        
                                        <div class="row g-3 align-items-end">
                                            <div class="col-md-5">
                                                <label class="form-label">المنتج</label>
                                                {{ form.product }}
                                                {% if form.product.errors %}
                                                    <div class="text-danger small">{{ form.product.errors.0 }}</div>
                                                {% endif %}
                                            </div>
                                            <div class="col-md-2">
                                                <label class="form-label">الكمية</label>
                                                {{ form.quantity }}
                                                {% if form.quantity.errors %}
                                                    <div class="text-danger small">{{ form.quantity.errors.0 }}</div>
                                                {% endif %}
                                            </div>
                                            <div class="col-md-3">
                                                <label class="form-label">سعر الوحدة (دج)</label>
                                                {{ form.unit_price }}
                                                {% if form.unit_price.errors %}
                                                    <div class="text-danger small">{{ form.unit_price.errors.0 }}</div>
                                                {% endif %}
                                            </div>
                                            <div class="col-md-2">
                                                {% if form.DELETE %}
                                                    {{ form.DELETE }}
                                                    <button type="button" class="btn btn-outline-danger btn-sm w-100" onclick="removeOrderItem(this)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                {% endif %}
                                            </div>
                                        </div>
                                        
                                        {% for hidden in form.hidden_fields %}
                                            {{ hidden }}
                                        {% endfor %}
                                    </div>
                                {% endfor %}
                            </div>
                            
                            <!-- ملخص المبالغ -->
                            <div class="row mt-4">
                                <div class="col-md-8"></div>
                                <div class="col-md-4">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between mb-2">
                                                <span>المجموع الفرعي:</span>
                                                <span id="subtotal">0.00 دج</span>
                                            </div>
                                            <div class="d-flex justify-content-between mb-2">
                                                <span>تكلفة الشحن:</span>
                                                <span id="shipping-cost">0.00 دج</span>
                                            </div>
                                            <div class="d-flex justify-content-between mb-2">
                                                <span>الخصم:</span>
                                                <span id="discount">0.00 دج</span>
                                            </div>
                                            <hr>
                                            <div class="d-flex justify-content-between fw-bold">
                                                <span>المبلغ الإجمالي:</span>
                                                <span id="total">0.00 دج</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات التوصيل -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">معلومات التوصيل</h6>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-12">
                                    <label for="{{ form.shipping_address.id_for_label }}" class="form-label">{{ form.shipping_address.label }}</label>
                                    {{ form.shipping_address }}
                                    {% if form.shipping_address.errors %}
                                        <div class="text-danger small">{{ form.shipping_address.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4">
                                    <label for="{{ form.shipping_city.id_for_label }}" class="form-label">{{ form.shipping_city.label }}</label>
                                    {{ form.shipping_city }}
                                    {% if form.shipping_city.errors %}
                                        <div class="text-danger small">{{ form.shipping_city.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4">
                                    <label for="{{ form.shipping_wilaya.id_for_label }}" class="form-label">{{ form.shipping_wilaya.label }}</label>
                                    {{ form.shipping_wilaya }}
                                    {% if form.shipping_wilaya.errors %}
                                        <div class="text-danger small">{{ form.shipping_wilaya.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4">
                                    <label for="{{ form.shipping_phone.id_for_label }}" class="form-label">{{ form.shipping_phone.label }}</label>
                                    {{ form.shipping_phone }}
                                    {% if form.shipping_phone.errors %}
                                        <div class="text-danger small">{{ form.shipping_phone.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- المبالغ والخصومات -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">المبالغ والخصومات</h6>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="{{ form.shipping_cost.id_for_label }}" class="form-label">{{ form.shipping_cost.label }}</label>
                                    <div class="input-group">
                                        {{ form.shipping_cost }}
                                        <span class="input-group-text">دج</span>
                                    </div>
                                    {% if form.shipping_cost.errors %}
                                        <div class="text-danger small">{{ form.shipping_cost.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6">
                                    <label for="{{ form.discount.id_for_label }}" class="form-label">{{ form.discount.label }}</label>
                                    <div class="input-group">
                                        {{ form.discount }}
                                        <span class="input-group-text">دج</span>
                                    </div>
                                    {% if form.discount.errors %}
                                        <div class="text-danger small">{{ form.discount.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- ملاحظات -->
                    {% if form.notes %}
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">ملاحظات</h6>
                        </div>
                        <div class="card-body">
                            <label for="{{ form.notes.id_for_label }}" class="form-label">{{ form.notes.label }}</label>
                            {{ form.notes }}
                            {% if form.notes.errors %}
                                <div class="text-danger small">{{ form.notes.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}

                    <!-- أزرار الحفظ والإلغاء -->
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ الطلب
                        </button>
                        <a href="{% url 'orders:list' %}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                        {% if object %}
                            <a href="{% url 'orders:detail' object.pk %}" class="btn btn-outline-info">
                                <i class="fas fa-eye me-2"></i>
                                عرض الطلب
                            </a>
                        {% endif %}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let itemIndex = {{ formset.total_form_count }};

function addOrderItem() {
    const container = document.getElementById('order-items');
    const emptyForm = document.querySelector('.order-item-form').cloneNode(true);
    
    // تحديث الفهارس في النموذج الجديد
    const formRegex = RegExp(`form-(\\d){1}-`, 'g');
    emptyForm.innerHTML = emptyForm.innerHTML.replace(formRegex, `form-${itemIndex}-`);
    
    // مسح القيم
    emptyForm.querySelectorAll('input, select').forEach(input => {
        if (input.type !== 'hidden') {
            input.value = '';
        }
    });
    
    container.appendChild(emptyForm);
    
    // تحديث عدد النماذج
    const totalForms = document.querySelector('#id_form-TOTAL_FORMS');
    totalForms.value = parseInt(totalForms.value) + 1;
    
    itemIndex++;
}

function removeOrderItem(button) {
    const formDiv = button.closest('.order-item-form');
    const deleteCheckbox = formDiv.querySelector('input[name$="-DELETE"]');
    
    if (deleteCheckbox) {
        deleteCheckbox.checked = true;
        formDiv.style.display = 'none';
    } else {
        formDiv.remove();
        // تحديث عدد النماذج
        const totalForms = document.querySelector('#id_form-TOTAL_FORMS');
        totalForms.value = parseInt(totalForms.value) - 1;
    }
    
    calculateTotal();
}

function calculateTotal() {
    let subtotal = 0;
    
    document.querySelectorAll('.order-item-form:not([style*="display: none"])').forEach(form => {
        const quantity = parseFloat(form.querySelector('input[name$="-quantity"]').value) || 0;
        const unitPrice = parseFloat(form.querySelector('input[name$="-unit_price"]').value) || 0;
        subtotal += quantity * unitPrice;
    });
    
    const shippingCost = parseFloat(document.querySelector('input[name="shipping_cost"]').value) || 0;
    const discount = parseFloat(document.querySelector('input[name="discount"]').value) || 0;
    const total = subtotal + shippingCost - discount;
    
    document.getElementById('subtotal').textContent = subtotal.toFixed(2) + ' دج';
    document.getElementById('shipping-cost').textContent = shippingCost.toFixed(2) + ' دج';
    document.getElementById('discount').textContent = discount.toFixed(2) + ' دج';
    document.getElementById('total').textContent = total.toFixed(2) + ' دج';
}

// تحديث الأسعار عند تغيير المنتج
document.addEventListener('change', function(e) {
    if (e.target.name && e.target.name.includes('product')) {
        const productSelect = e.target;
        const form = productSelect.closest('.order-item-form');
        const unitPriceInput = form.querySelector('input[name$="-unit_price"]');
        
        if (productSelect.value) {
            // يمكن إضافة AJAX call هنا لجلب سعر المنتج
            // للآن سنتركه فارغ
        }
    }
    
    if (e.target.name && (e.target.name.includes('quantity') || e.target.name.includes('unit_price') || e.target.name === 'shipping_cost' || e.target.name === 'discount')) {
        calculateTotal();
    }
});

// حساب المجموع عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    calculateTotal();
});
</script>
{% endblock %}
