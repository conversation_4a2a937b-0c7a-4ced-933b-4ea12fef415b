{% extends 'base.html' %}

{% block title %}{{ title }} - مدير التجارة الإلكترونية{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item"><a href="{% url 'orders:list' %}">الطلبيات</a></li>
<li class="breadcrumb-item active">{{ title }}</li>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-plus me-2"></i>
                    {{ title }}
                </h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <!-- معلومات الطلب الأساسية -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">معلومات الطلب</h6>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-12">
                                    <label for="{{ form.customer.id_for_label }}" class="form-label">{{ form.customer.label }}</label>
                                    {{ form.customer }}
                                    {% if form.customer.errors %}
                                        <div class="text-danger small">{{ form.customer.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">اختر الزبون الذي سيتم إنشاء الطلب له</div>
                                </div>
                                <div class="col-md-8">
                                    <label for="{{ form.product.id_for_label }}" class="form-label">{{ form.product.label }}</label>
                                    {{ form.product }}
                                    {% if form.product.errors %}
                                        <div class="text-danger small">{{ form.product.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">اختر المنتج المطلوب</div>
                                </div>
                                <div class="col-md-4">
                                    <label for="{{ form.quantity.id_for_label }}" class="form-label">{{ form.quantity.label }}</label>
                                    {{ form.quantity }}
                                    {% if form.quantity.errors %}
                                        <div class="text-danger small">{{ form.quantity.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">عدد القطع المطلوبة</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات إضافية -->
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>ملاحظة:</h6>
                        <ul class="mb-0">
                            <li>سيتم استخدام العنوان الافتراضي للزبون كعنوان التوصيل</li>
                            <li>سيتم تعيين حالة الطلب كـ "جديد" وحالة الدفع كـ "في الانتظار"</li>
                            <li>يمكنك تعديل تفاصيل الطلب لاحقاً من صفحة تفاصيل الطلب</li>
                        </ul>
                    </div>

                    <!-- أزرار الحفظ والإلغاء -->
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            إنشاء الطلب
                        </button>
                        <a href="{% url 'orders:list' %}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- معلومات مساعدة -->
<div class="row justify-content-center mt-4">
    <div class="col-lg-8">
        <div class="card border-success">
            <div class="card-header bg-success text-white">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    نصائح لإنشاء طلب ناجح
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>اختيار الزبون:</h6>
                        <ul class="small">
                            <li>تأكد من أن معلومات الزبون محدثة</li>
                            <li>تحقق من وجود عنوان افتراضي للزبون</li>
                        </ul>
                        
                        <h6>اختيار المنتج:</h6>
                        <ul class="small">
                            <li>تأكد من توفر المنتج في المخزون</li>
                            <li>تحقق من سعر المنتج الحالي</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>الكمية:</h6>
                        <ul class="small">
                            <li>تأكد من أن الكمية متوفرة في المخزون</li>
                            <li>يمكن إضافة منتجات أخرى لاحقاً</li>
                        </ul>
                        
                        <h6>بعد الإنشاء:</h6>
                        <ul class="small">
                            <li>يمكنك تعديل عنوان التوصيل</li>
                            <li>يمكنك إضافة منتجات أخرى للطلب</li>
                            <li>يمكنك تحديث حالة الطلب والدفع</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// تحديث معلومات المنتج عند الاختيار
document.addEventListener('DOMContentLoaded', function() {
    const productSelect = document.getElementById('{{ form.product.id_for_label }}');
    const quantityInput = document.getElementById('{{ form.quantity.id_for_label }}');
    
    productSelect.addEventListener('change', function() {
        if (this.value) {
            // يمكن إضافة AJAX هنا لجلب معلومات المنتج
            console.log('تم اختيار المنتج:', this.value);
        }
    });
    
    // التحقق من الكمية
    quantityInput.addEventListener('input', function() {
        if (this.value < 1) {
            this.value = 1;
        }
    });
});
</script>
{% endblock %}
