{% extends 'base.html' %}

{% block title %}إضافة شركة شحن جديدة - مدير التجارة الإلكترونية{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item"><a href="{% url 'shipping:list' %}">الشحن والتوصيل</a></li>
<li class="breadcrumb-item"><a href="{% url 'shipping:companies' %}">شركات الشحن</a></li>
<li class="breadcrumb-item active">إضافة شركة جديدة</li>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-building me-2"></i>
                    إضافة شركة شحن جديدة
                </h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <!-- معلومات الشركة الأساسية -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">معلومات الشركة</h6>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-12">
                                    <label for="{{ form.name.id_for_label }}" class="form-label">{{ form.name.label }}</label>
                                    {{ form.name }}
                                    {% if form.name.errors %}
                                        <div class="text-danger small">{{ form.name.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6">
                                    <label for="{{ form.phone.id_for_label }}" class="form-label">{{ form.phone.label }}</label>
                                    {{ form.phone }}
                                    {% if form.phone.errors %}
                                        <div class="text-danger small">{{ form.phone.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6">
                                    <label for="{{ form.email.id_for_label }}" class="form-label">{{ form.email.label }}</label>
                                    {{ form.email }}
                                    {% if form.email.errors %}
                                        <div class="text-danger small">{{ form.email.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-12">
                                    <label for="{{ form.website.id_for_label }}" class="form-label">{{ form.website.label }}</label>
                                    {{ form.website }}
                                    {% if form.website.errors %}
                                        <div class="text-danger small">{{ form.website.errors.0 }}</div>
                                    {% endif %}
                                    {% if form.website.help_text %}
                                        <div class="form-text">{{ form.website.help_text }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- ملاحظات وإعدادات -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">معلومات إضافية</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="{{ form.notes.id_for_label }}" class="form-label">{{ form.notes.label }}</label>
                                {{ form.notes }}
                                {% if form.notes.errors %}
                                    <div class="text-danger small">{{ form.notes.errors.0 }}</div>
                                {% endif %}
                                {% if form.notes.help_text %}
                                    <div class="form-text">{{ form.notes.help_text }}</div>
                                {% endif %}
                            </div>
                            <div class="form-check form-switch">
                                {{ form.is_active }}
                                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                    {{ form.is_active.label }}
                                </label>
                                {% if form.is_active.errors %}
                                    <div class="text-danger small">{{ form.is_active.errors.0 }}</div>
                                {% endif %}
                                {% if form.is_active.help_text %}
                                    <div class="form-text">{{ form.is_active.help_text }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الحفظ والإلغاء -->
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ الشركة
                        </button>
                        <a href="{% url 'shipping:companies' %}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
