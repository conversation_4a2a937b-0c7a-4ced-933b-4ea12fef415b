from django.db import models
from django.core.validators import MinValueValidator


class ShippingCompany(models.Model):
    """نموذج شركات الشحن"""
    name = models.CharField('اسم الشركة', max_length=100, unique=True)
    phone = models.CharField('رقم الهاتف', max_length=20, blank=True, null=True)
    email = models.EmailField('البريد الإلكتروني', blank=True, null=True)
    website = models.URLField('الموقع الإلكتروني', blank=True, null=True)
    is_active = models.BooleanField('نشط', default=True)
    notes = models.TextField('ملاحظات', blank=True, null=True)
    created_at = models.DateTimeField('تاريخ الإنشاء', auto_now_add=True)

    class Meta:
        verbose_name = 'شركة شحن'
        verbose_name_plural = 'شركات الشحن'
        ordering = ['name']

    def __str__(self):
        return self.name


class ShippingZone(models.Model):
    """نموذج مناطق الشحن"""
    ZONE_TYPE_CHOICES = [
        ('wilaya', 'ولاية'),
        ('city', 'مدينة'),
        ('region', 'منطقة'),
    ]

    name = models.CharField('اسم المنطقة', max_length=100)
    zone_type = models.CharField('نوع المنطقة', max_length=10, choices=ZONE_TYPE_CHOICES, default='wilaya')
    is_active = models.BooleanField('نشط', default=True)

    class Meta:
        verbose_name = 'منطقة شحن'
        verbose_name_plural = 'مناطق الشحن'
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.get_zone_type_display()})"


class ShippingRate(models.Model):
    """نموذج أسعار الشحن"""
    company = models.ForeignKey(ShippingCompany, on_delete=models.CASCADE, related_name='rates', verbose_name='شركة الشحن')
    zone = models.ForeignKey(ShippingZone, on_delete=models.CASCADE, related_name='rates', verbose_name='المنطقة')
    base_price = models.DecimalField('السعر الأساسي', max_digits=10, decimal_places=2, validators=[MinValueValidator(0)])
    price_per_kg = models.DecimalField('السعر لكل كيلو', max_digits=10, decimal_places=2, default=0, validators=[MinValueValidator(0)])
    free_shipping_threshold = models.DecimalField('حد الشحن المجاني', max_digits=10, decimal_places=2, blank=True, null=True, validators=[MinValueValidator(0)])
    delivery_days_min = models.PositiveIntegerField('أقل مدة توصيل (أيام)', default=1)
    delivery_days_max = models.PositiveIntegerField('أقصى مدة توصيل (أيام)', default=3)
    is_active = models.BooleanField('نشط', default=True)

    class Meta:
        verbose_name = 'سعر شحن'
        verbose_name_plural = 'أسعار الشحن'
        unique_together = ['company', 'zone']

    def __str__(self):
        return f"{self.company.name} - {self.zone.name}: {self.base_price} دج"

    def calculate_shipping_cost(self, order_total, weight=0):
        """حساب تكلفة الشحن"""
        if self.free_shipping_threshold and order_total >= self.free_shipping_threshold:
            return 0

        cost = self.base_price
        if weight > 0:
            cost += weight * self.price_per_kg

        return cost


class Shipment(models.Model):
    """نموذج الشحنات"""
    STATUS_CHOICES = [
        ('pending', 'في الانتظار'),
        ('picked_up', 'تم الاستلام'),
        ('in_transit', 'في الطريق'),
        ('out_for_delivery', 'خرج للتوصيل'),
        ('delivered', 'تم التوصيل'),
        ('failed', 'فشل التوصيل'),
        ('returned', 'مرتجع'),
    ]

    order = models.OneToOneField('orders.Order', on_delete=models.CASCADE, related_name='shipment', verbose_name='الطلب')
    company = models.ForeignKey(ShippingCompany, on_delete=models.CASCADE, verbose_name='شركة الشحن')
    tracking_number = models.CharField('رقم التتبع', max_length=100, blank=True, null=True)
    status = models.CharField('حالة الشحنة', max_length=20, choices=STATUS_CHOICES, default='pending')
    weight = models.DecimalField('الوزن (كيلو)', max_digits=5, decimal_places=2, blank=True, null=True)
    shipping_cost = models.DecimalField('تكلفة الشحن', max_digits=10, decimal_places=2, default=0)
    notes = models.TextField('ملاحظات', blank=True, null=True)

    # تواريخ مهمة
    created_at = models.DateTimeField('تاريخ الإنشاء', auto_now_add=True)
    picked_up_at = models.DateTimeField('تاريخ الاستلام', blank=True, null=True)
    delivered_at = models.DateTimeField('تاريخ التوصيل', blank=True, null=True)

    class Meta:
        verbose_name = 'شحنة'
        verbose_name_plural = 'الشحنات'
        ordering = ['-created_at']

    def __str__(self):
        return f"شحنة {self.order.order_number} - {self.company.name}"

    @property
    def estimated_delivery_date(self):
        """تاريخ التوصيل المتوقع"""
        if self.picked_up_at:
            from datetime import timedelta
            rate = ShippingRate.objects.filter(company=self.company).first()
            if rate:
                return self.picked_up_at.date() + timedelta(days=rate.delivery_days_max)
        return None


class ShipmentTracking(models.Model):
    """نموذج تتبع الشحنات"""
    shipment = models.ForeignKey(Shipment, on_delete=models.CASCADE, related_name='tracking_history', verbose_name='الشحنة')
    status = models.CharField('الحالة', max_length=20, choices=Shipment.STATUS_CHOICES)
    location = models.CharField('الموقع', max_length=200, blank=True, null=True)
    description = models.TextField('الوصف', blank=True, null=True)
    timestamp = models.DateTimeField('الوقت', auto_now_add=True)

    class Meta:
        verbose_name = 'تتبع شحنة'
        verbose_name_plural = 'تتبع الشحنات'
        ordering = ['-timestamp']

    def __str__(self):
        return f"{self.shipment.order.order_number} - {self.get_status_display()}"
