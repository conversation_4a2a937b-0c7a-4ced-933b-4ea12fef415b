from django.shortcuts import render
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.http import HttpResponse

# Views مؤقتة للاختبار
class ShipmentListView(ListView):
    def get(self, request):
        return HttpResponse("صفحة الشحنات - قيد التطوير")

class ShippingCompanyListView(ListView):
    def get(self, request):
        return HttpResponse("شركات الشحن - قيد التطوير")

class ShippingCompanyCreateView(CreateView):
    def get(self, request):
        return HttpResponse("إضافة شركة شحن - قيد التطوير")

class ShippingRateListView(ListView):
    def get(self, request):
        return HttpResponse("أسعار الشحن - قيد التطوير")

class ShippingRateCreateView(CreateView):
    def get(self, request):
        return HttpResponse("إضافة سعر شحن - قيد التطوير")

class ShipmentDetailView(DetailView):
    def get(self, request, pk):
        return HttpResponse("تفاصيل الشحنة - قيد التطوير")

class ShipmentTrackingView(DetailView):
    def get(self, request, pk):
        return HttpResponse("تتبع الشحنة - قيد التطوير")

class ShippingLabelView(DetailView):
    def get(self, request, pk):
        return HttpResponse("ملصق الشحن - قيد التطوير")
