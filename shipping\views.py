from django.shortcuts import render, get_object_or_404, redirect
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.contrib import messages
from django.urls import reverse_lazy
from django.db.models import Q, Count
from django.http import JsonResponse, HttpResponse
from django.template.loader import render_to_string
from django.utils import timezone

from .models import Shipment, ShippingCompany, ShippingRate, ShippingZone, ShipmentTracking
from .forms import ShipmentForm, ShippingCompanyForm, ShippingRateForm, ShippingZoneForm
from orders.models import Order


class ShipmentListView(ListView):
    """عرض قائمة الشحنات"""
    model = Shipment
    template_name = 'shipping/list.html'
    context_object_name = 'shipments'
    paginate_by = 20

    def get_queryset(self):
        queryset = Shipment.objects.select_related('order', 'company').order_by('-created_at')

        # البحث
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(order__order_number__icontains=search) |
                Q(tracking_number__icontains=search) |
                Q(order__customer__first_name__icontains=search) |
                Q(order__customer__last_name__icontains=search)
            )

        # تصفية حسب الحالة
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)

        # تصفية حسب شركة الشحن
        company = self.request.GET.get('company')
        if company:
            queryset = queryset.filter(company_id=company)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['companies'] = ShippingCompany.objects.filter(is_active=True)
        context['status_choices'] = Shipment.STATUS_CHOICES
        context['search'] = self.request.GET.get('search', '')
        context['selected_status'] = self.request.GET.get('status', '')
        context['selected_company'] = self.request.GET.get('company', '')

        # إحصائيات سريعة
        context['total_shipments'] = self.get_queryset().count()
        context['pending_shipments'] = self.get_queryset().filter(status='pending').count()
        context['in_transit_shipments'] = self.get_queryset().filter(status='in_transit').count()
        context['delivered_shipments'] = self.get_queryset().filter(status='delivered').count()

        return context


class ShippingCompanyListView(ListView):
    """عرض قائمة شركات الشحن"""
    model = ShippingCompany
    template_name = 'shipping/companies.html'
    context_object_name = 'companies'

    def get_queryset(self):
        queryset = ShippingCompany.objects.annotate(
            shipment_count=Count('shipment')
        ).order_by('name')

        # البحث
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(phone__icontains=search) |
                Q(email__icontains=search)
            )

        # تصفية حسب الحالة
        status = self.request.GET.get('status')
        if status == 'active':
            queryset = queryset.filter(is_active=True)
        elif status == 'inactive':
            queryset = queryset.filter(is_active=False)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search'] = self.request.GET.get('search', '')
        context['selected_status'] = self.request.GET.get('status', '')
        context['active_companies'] = ShippingCompany.objects.filter(is_active=True).count()
        context['total_shipments'] = Shipment.objects.count()
        context['pending_shipments'] = Shipment.objects.filter(status='pending').count()
        return context


class ShippingCompanyCreateView(CreateView):
    """إضافة شركة شحن جديدة"""
    model = ShippingCompany
    form_class = ShippingCompanyForm
    template_name = 'shipping/company_form.html'
    success_url = reverse_lazy('shipping:companies')

    def form_valid(self, form):
        messages.success(self.request, f'تم إضافة شركة الشحن {form.instance.name} بنجاح')
        return super().form_valid(form)


class ShippingRateListView(ListView):
    """عرض قائمة أسعار الشحن"""
    model = ShippingRate
    template_name = 'shipping/rates.html'
    context_object_name = 'rates'

    def get_queryset(self):
        queryset = ShippingRate.objects.select_related('company', 'zone').order_by('company__name', 'zone__name')

        # تصفية حسب الشركة
        company = self.request.GET.get('company')
        if company:
            queryset = queryset.filter(company_id=company)

        # تصفية حسب المنطقة
        zone = self.request.GET.get('zone')
        if zone:
            queryset = queryset.filter(zone_id=zone)

        # تصفية حسب الحالة
        status = self.request.GET.get('status')
        if status == 'active':
            queryset = queryset.filter(is_active=True)
        elif status == 'inactive':
            queryset = queryset.filter(is_active=False)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['selected_company'] = self.request.GET.get('company', '')
        context['selected_zone'] = self.request.GET.get('zone', '')
        context['selected_status'] = self.request.GET.get('status', '')
        context['companies'] = ShippingCompany.objects.filter(is_active=True).order_by('name')
        context['zones'] = ShippingZone.objects.filter(is_active=True).order_by('name')
        context['active_rates'] = ShippingRate.objects.filter(is_active=True).count()
        context['companies_count'] = ShippingCompany.objects.filter(is_active=True).count()
        context['zones_count'] = ShippingZone.objects.filter(is_active=True).count()
        return context


class ShippingRateCreateView(CreateView):
    """إضافة سعر شحن جديد"""
    model = ShippingRate
    form_class = ShippingRateForm
    template_name = 'shipping/rate_form.html'
    success_url = reverse_lazy('shipping:rates')

    def form_valid(self, form):
        messages.success(self.request, 'تم إضافة سعر الشحن بنجاح')
        return super().form_valid(form)


class ShipmentDetailView(DetailView):
    """عرض تفاصيل الشحنة"""
    model = Shipment
    template_name = 'shipping/detail.html'
    context_object_name = 'shipment'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['tracking_history'] = self.object.tracking_history.order_by('-timestamp')
        return context


class ShipmentTrackingView(DetailView):
    """تتبع الشحنة"""
    model = Shipment
    template_name = 'shipping/tracking.html'
    context_object_name = 'shipment'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['tracking_history'] = self.object.tracking_history.order_by('timestamp')
        return context


class ShippingLabelView(DetailView):
    """ملصق الشحن"""
    model = Shipment
    template_name = 'shipping/label.html'
    context_object_name = 'shipment'

    def get(self, request, *args, **kwargs):
        self.object = self.get_object()
        context = self.get_context_data(object=self.object)

        # إذا كان الطلب لتحميل PDF
        if request.GET.get('format') == 'pdf':
            # يمكن إضافة توليد PDF هنا لاحقاً
            return HttpResponse("توليد PDF قيد التطوير")

        return self.render_to_response(context)
