from django.shortcuts import render
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.http import HttpResponse

# Views مؤقتة للاختبار
class ReportsDashboardView(ListView):
    def get(self, request):
        return HttpResponse("لوحة التقارير - قيد التطوير")

class SalesReportView(ListView):
    def get(self, request):
        return HttpResponse("تقرير المبيعات - قيد التطوير")

class ProductsReportView(ListView):
    def get(self, request):
        return HttpResponse("تقرير المنتجات - قيد التطوير")

class CustomersReportView(ListView):
    def get(self, request):
        return HttpResponse("تقرير الزبائن - قيد التطوير")

class ExportOrdersView(ListView):
    def get(self, request):
        return HttpResponse("تصدير الطلبيات - قيد التطوير")

class ExportProductsView(ListView):
    def get(self, request):
        return HttpResponse("تصدير المنتجات - قيد التطوير")
