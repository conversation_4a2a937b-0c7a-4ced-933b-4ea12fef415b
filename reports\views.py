from django.shortcuts import render
from django.views.generic import ListView, TemplateView
from django.http import HttpResponse, JsonResponse
from django.db.models import Sum, Count, Avg, Q, F
from django.utils import timezone
from datetime import datetime, timedelta
import json
import openpyxl
from openpyxl.styles import Font, Alignment

from orders.models import Order, OrderItem
from products.models import Product
from customers.models import Customer
from settings_app.models import StoreSettings


class ReportsDashboardView(TemplateView):
    """لوحة التقارير الرئيسية"""
    template_name = 'reports/dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # الفترة الزمنية (آخر 30 يوم)
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=30)

        # إحصائيات عامة
        context['total_orders'] = Order.objects.count()
        context['total_revenue'] = Order.objects.filter(status='delivered').aggregate(
            total=Sum('total_amount')
        )['total'] or 0
        context['total_customers'] = Customer.objects.count()
        context['total_products'] = Product.objects.count()

        # إحصائيات الفترة الحالية
        current_period_orders = Order.objects.filter(created_at__date__range=[start_date, end_date])
        context['current_period_orders'] = current_period_orders.count()
        context['current_period_revenue'] = current_period_orders.filter(status='delivered').aggregate(
            total=Sum('total_amount')
        )['total'] or 0

        # مقارنة مع الفترة السابقة
        prev_start_date = start_date - timedelta(days=30)
        prev_end_date = start_date
        prev_period_orders = Order.objects.filter(created_at__date__range=[prev_start_date, prev_end_date])
        prev_period_revenue = prev_period_orders.filter(status='delivered').aggregate(
            total=Sum('total_amount')
        )['total'] or 0

        # حساب نسبة النمو
        if prev_period_revenue > 0:
            context['revenue_growth'] = ((context['current_period_revenue'] - prev_period_revenue) / prev_period_revenue) * 100
        else:
            context['revenue_growth'] = 100 if context['current_period_revenue'] > 0 else 0

        # المنتجات الأكثر مبيعاً
        context['top_products'] = Product.objects.annotate(
            total_sold=Sum('orderitem__quantity', filter=Q(orderitem__order__status='delivered'))
        ).filter(total_sold__gt=0).order_by('-total_sold')[:5]

        # الزبائن الأكثر شراءً
        context['top_customers'] = Customer.objects.annotate(
            total_spent=Sum('orders__total_amount', filter=Q(orders__status='delivered'))
        ).filter(total_spent__gt=0).order_by('-total_spent')[:5]

        # بيانات المبيعات اليومية لآخر 7 أيام
        daily_sales = []
        daily_labels = []
        for i in range(6, -1, -1):
            date = end_date - timedelta(days=i)
            daily_revenue = Order.objects.filter(
                created_at__date=date,
                status='delivered'
            ).aggregate(total=Sum('total_amount'))['total'] or 0

            daily_sales.append(float(daily_revenue))
            daily_labels.append(date.strftime('%d/%m'))

        context['daily_sales_data'] = json.dumps(daily_sales)
        context['daily_sales_labels'] = json.dumps(daily_labels)

        # توزيع حالات الطلبيات
        order_status_data = []
        order_status_labels = []
        for status_code, status_name in Order.STATUS_CHOICES:
            count = Order.objects.filter(status=status_code).count()
            if count > 0:
                order_status_data.append(count)
                order_status_labels.append(status_name)

        context['order_status_data'] = json.dumps(order_status_data)
        context['order_status_labels'] = json.dumps(order_status_labels)

        return context


class SalesReportView(TemplateView):
    """تقرير المبيعات"""
    template_name = 'reports/sales.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # الحصول على الفترة من المعاملات
        date_from = self.request.GET.get('date_from')
        date_to = self.request.GET.get('date_to')

        if not date_from:
            date_from = (timezone.now().date() - timedelta(days=30)).strftime('%Y-%m-%d')
        if not date_to:
            date_to = timezone.now().date().strftime('%Y-%m-%d')

        context['date_from'] = date_from
        context['date_to'] = date_to

        # تصفية الطلبيات حسب الفترة
        orders = Order.objects.filter(
            created_at__date__range=[date_from, date_to],
            status='delivered'
        )

        # إحصائيات المبيعات
        context['total_orders'] = orders.count()
        context['total_revenue'] = orders.aggregate(total=Sum('total_amount'))['total'] or 0
        context['average_order_value'] = orders.aggregate(avg=Avg('total_amount'))['avg'] or 0
        context['total_items_sold'] = OrderItem.objects.filter(
            order__in=orders
        ).aggregate(total=Sum('quantity'))['total'] or 0

        # المبيعات حسب المنتج
        context['product_sales'] = Product.objects.annotate(
            total_sold=Sum('orderitem__quantity', filter=Q(orderitem__order__in=orders)),
            total_revenue=Sum(F('orderitem__quantity') * F('orderitem__unit_price'), filter=Q(orderitem__order__in=orders))
        ).filter(total_sold__gt=0).order_by('-total_revenue')

        return context


class ProductsReportView(TemplateView):
    """تقرير المنتجات"""
    template_name = 'reports/products.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # إحصائيات المنتجات
        context['total_products'] = Product.objects.count()
        context['active_products'] = Product.objects.filter(is_active=True).count()
        context['low_stock_products'] = Product.objects.filter(quantity__lte=F('min_quantity')).count()
        context['out_of_stock_products'] = Product.objects.filter(quantity=0).count()

        # المنتجات حسب الفئة
        context['products_by_category'] = Product.objects.values(
            'category__name'
        ).annotate(
            count=Count('id'),
            total_value=Sum(F('quantity') * F('price'))
        ).order_by('-count')

        # المنتجات الأكثر مبيعاً
        context['best_selling_products'] = Product.objects.annotate(
            total_sold=Sum('orderitem__quantity', filter=Q(orderitem__order__status='delivered'))
        ).filter(total_sold__gt=0).order_by('-total_sold')[:10]

        # المنتجات الأقل مبيعاً
        context['least_selling_products'] = Product.objects.annotate(
            total_sold=Sum('orderitem__quantity', filter=Q(orderitem__order__status='delivered'))
        ).filter(total_sold__isnull=True).order_by('-created_at')[:10]

        return context


class CustomersReportView(TemplateView):
    """تقرير الزبائن"""
    template_name = 'reports/customers.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # إحصائيات الزبائن
        context['total_customers'] = Customer.objects.count()
        context['active_customers'] = Customer.objects.filter(is_active=True).count()
        context['customers_with_orders'] = Customer.objects.filter(orders__isnull=False).distinct().count()

        # الزبائن الجدد هذا الشهر
        context['new_customers_this_month'] = Customer.objects.filter(
            created_at__month=timezone.now().month,
            created_at__year=timezone.now().year
        ).count()

        # أفضل الزبائن
        context['top_customers'] = Customer.objects.annotate(
            total_orders=Count('orders'),
            total_spent=Sum('orders__total_amount', filter=Q(orders__status='delivered'))
        ).filter(total_spent__gt=0).order_by('-total_spent')[:10]

        # الزبائن حسب الجنس
        context['customers_by_gender'] = Customer.objects.values('gender').annotate(
            count=Count('id')
        ).order_by('-count')

        return context


class ExportOrdersView(TemplateView):
    """تصدير الطلبيات إلى Excel"""

    def get(self, request, *args, **kwargs):
        # إنشاء ملف Excel
        workbook = openpyxl.Workbook()
        worksheet = workbook.active
        worksheet.title = "الطلبيات"

        # إعداد العناوين
        headers = [
            'رقم الطلب', 'الزبون', 'رقم الهاتف', 'المبلغ الإجمالي',
            'حالة الطلب', 'حالة الدفع', 'تاريخ الطلب', 'تاريخ التوصيل'
        ]

        # كتابة العناوين
        for col, header in enumerate(headers, 1):
            cell = worksheet.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')

        # الحصول على الطلبيات
        orders = Order.objects.select_related('customer').order_by('-created_at')

        # كتابة البيانات
        for row, order in enumerate(orders, 2):
            worksheet.cell(row=row, column=1, value=order.order_number)
            worksheet.cell(row=row, column=2, value=order.customer.full_name)
            worksheet.cell(row=row, column=3, value=order.customer.phone)
            worksheet.cell(row=row, column=4, value=float(order.total_amount))
            worksheet.cell(row=row, column=5, value=order.get_status_display())
            worksheet.cell(row=row, column=6, value=order.get_payment_status_display())
            worksheet.cell(row=row, column=7, value=order.created_at.strftime('%Y-%m-%d %H:%M'))
            worksheet.cell(row=row, column=8, value=order.delivered_at.strftime('%Y-%m-%d %H:%M') if order.delivered_at else '')

        # إعداد الاستجابة
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = 'attachment; filename="orders.xlsx"'

        workbook.save(response)
        return response


class ExportProductsView(TemplateView):
    """تصدير المنتجات إلى Excel"""

    def get(self, request, *args, **kwargs):
        # إنشاء ملف Excel
        workbook = openpyxl.Workbook()
        worksheet = workbook.active
        worksheet.title = "المنتجات"

        # إعداد العناوين
        headers = [
            'اسم المنتج', 'رمز المنتج', 'الفئة', 'السعر',
            'سعر التكلفة', 'الكمية المتوفرة', 'الحد الأدنى', 'الحالة'
        ]

        # كتابة العناوين
        for col, header in enumerate(headers, 1):
            cell = worksheet.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')

        # الحصول على المنتجات
        products = Product.objects.select_related('category').order_by('name')

        # كتابة البيانات
        for row, product in enumerate(products, 2):
            worksheet.cell(row=row, column=1, value=product.name)
            worksheet.cell(row=row, column=2, value=product.sku or '')
            worksheet.cell(row=row, column=3, value=product.category.name if product.category else '')
            worksheet.cell(row=row, column=4, value=float(product.price))
            worksheet.cell(row=row, column=5, value=float(product.cost_price) if product.cost_price else '')
            worksheet.cell(row=row, column=6, value=product.quantity)
            worksheet.cell(row=row, column=7, value=product.min_quantity)
            worksheet.cell(row=row, column=8, value='نشط' if product.is_active else 'غير نشط')

        # إعداد الاستجابة
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = 'attachment; filename="products.xlsx"'

        workbook.save(response)
        return response
