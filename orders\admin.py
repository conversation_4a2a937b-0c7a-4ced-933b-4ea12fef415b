from django.contrib import admin
from .models import Order, OrderItem, OrderStatusHistory


class OrderItemInline(admin.TabularInline):
    model = OrderItem
    extra = 0


class OrderStatusHistoryInline(admin.TabularInline):
    model = OrderStatusHistory
    extra = 0
    readonly_fields = ['created_at']


@admin.register(Order)
class OrderAdmin(admin.ModelAdmin):
    list_display = ['order_number', 'customer', 'total_amount', 'status', 'payment_status', 'created_at']
    list_filter = ['status', 'payment_status', 'created_at']
    search_fields = ['order_number', 'customer__first_name', 'customer__last_name', 'customer__phone']
    list_editable = ['status', 'payment_status']
    ordering = ['-created_at']
    inlines = [OrderItemInline, OrderStatusHistoryInline]
    readonly_fields = ['order_number', 'subtotal', 'total_amount']

    fieldsets = (
        ('معلومات الطلب', {
            'fields': ('order_number', 'customer', 'status', 'payment_status')
        }),
        ('معلومات التوصيل', {
            'fields': ('shipping_address', 'shipping_city', 'shipping_wilaya', 'shipping_phone')
        }),
        ('المبالغ', {
            'fields': ('subtotal', 'shipping_cost', 'discount', 'total_amount')
        }),
        ('ملاحظات وتواريخ', {
            'fields': ('notes', 'shipped_at', 'delivered_at')
        }),
    )


@admin.register(OrderItem)
class OrderItemAdmin(admin.ModelAdmin):
    list_display = ['order', 'product', 'quantity', 'unit_price', 'total_price']
    list_filter = ['order__status', 'order__created_at']
    search_fields = ['order__order_number', 'product__name']


@admin.register(OrderStatusHistory)
class OrderStatusHistoryAdmin(admin.ModelAdmin):
    list_display = ['order', 'status', 'created_at']
    list_filter = ['status', 'created_at']
    search_fields = ['order__order_number']
    readonly_fields = ['created_at']
