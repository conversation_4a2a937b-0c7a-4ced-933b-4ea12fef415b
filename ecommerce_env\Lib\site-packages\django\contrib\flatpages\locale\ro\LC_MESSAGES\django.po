# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON>, 2019
# <PERSON>, 2011
# <PERSON> <<EMAIL>>, 2014
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON><PERSON> <razvan.s<PERSON><PERSON><PERSON>@gmail.com>, 2016
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-01-16 20:42+0100\n"
"PO-Revision-Date: 2019-01-18 12:01+0000\n"
"Last-Translator: Bo<PERSON><PERSON>\n"
"Language-Team: Romanian (http://www.transifex.com/django/django/language/"
"ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?"
"2:1));\n"

msgid "Advanced options"
msgstr "Opțiuni avansate"

msgid "Flat Pages"
msgstr "Pagini Statice"

msgid "URL"
msgstr "URL"

msgid ""
"Example: '/about/contact/'. Make sure to have leading and trailing slashes."
msgstr ""
"Exemplu: '/despre/contact/'. Asigurați-vă că sunt bare oblice la început și "
"la sfârșit."

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr ""
"Această valoare trebuie să conţină doar litere, numere, puncte, linii joase, "
"cratime, bare oblice sau tilde."

msgid "Example: '/about/contact'. Make sure to have a leading slash."
msgstr ""
"Exemplu: '/despre/contact'. Asigurați-vă că sunt bare oblice la început și "
"la sfârșit."

msgid "URL is missing a leading slash."
msgstr "În URL lipseste slashul inițial."

msgid "URL is missing a trailing slash."
msgstr "În URL lipseste slashul final."

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr "Pagina Flat cu url-ul %(url)s există deja pentru saitul %(site)s"

msgid "title"
msgstr "titlu"

msgid "content"
msgstr "conținut"

msgid "enable comments"
msgstr "permite comentarii"

msgid "template name"
msgstr "nume șablon"

msgid ""
"Example: 'flatpages/contact_page.html'. If this isn't provided, the system "
"will use 'flatpages/default.html'."
msgstr ""
"Exemplu: 'flatpages/contact_page.html'. Dacă aceasta nu există, sistemul va "
"folosi 'flatpages/default.html'."

msgid "registration required"
msgstr "necesită înregistrare"

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr ""
"Dacă aceasta este bifată, numai utilizatorii autentificați vor putea vedea "
"pagina."

msgid "sites"
msgstr "pagini"

msgid "flat page"
msgstr "pagină statică"

msgid "flat pages"
msgstr "pagini statice"
