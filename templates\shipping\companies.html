{% extends 'base.html' %}

{% block title %}شركات الشحن - مدير التجارة الإلكترونية{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item"><a href="{% url 'shipping:list' %}">الشحن والتوصيل</a></li>
<li class="breadcrumb-item active">شركات الشحن</li>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">إدارة شركات الشحن</h1>
    <div>
        <a href="{% url 'shipping:company_create' %}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>
            إضافة شركة جديدة
        </a>
        <a href="{% url 'shipping:list' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للشحنات
        </a>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center border-primary">
            <div class="card-body">
                <h4 class="text-primary">{{ companies.count }}</h4>
                <p class="card-text">إجمالي الشركات</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-success">
            <div class="card-body">
                <h4 class="text-success">{{ active_companies }}</h4>
                <p class="card-text">شركات نشطة</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-info">
            <div class="card-body">
                <h4 class="text-info">{{ total_shipments }}</h4>
                <p class="card-text">إجمالي الشحنات</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-warning">
            <div class="card-body">
                <h4 class="text-warning">{{ pending_shipments }}</h4>
                <p class="card-text">شحنات في الانتظار</p>
            </div>
        </div>
    </div>
</div>

<!-- البحث والتصفية -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-6">
                <input type="text" name="search" class="form-control" placeholder="البحث في الشركات..." value="{{ search }}">
            </div>
            <div class="col-md-4">
                <select name="status" class="form-select">
                    <option value="">جميع الحالات</option>
                    <option value="active" {% if selected_status == 'active' %}selected{% endif %}>نشط</option>
                    <option value="inactive" {% if selected_status == 'inactive' %}selected{% endif %}>غير نشط</option>
                </select>
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-outline-primary w-100">
                    <i class="fas fa-search"></i>
                    بحث
                </button>
            </div>
        </form>
    </div>
</div>

<!-- قائمة الشركات -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-building me-2"></i>
            قائمة شركات الشحن
        </h5>
    </div>
    <div class="card-body">
        {% if companies %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>اسم الشركة</th>
                            <th>معلومات الاتصال</th>
                            <th>الموقع الإلكتروني</th>
                            <th>عدد الشحنات</th>
                            <th>الحالة</th>
                            <th>تاريخ الإضافة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for company in companies %}
                        <tr>
                            <td>
                                <div>
                                    <strong>{{ company.name }}</strong>
                                    {% if company.notes %}
                                        <br><small class="text-muted">{{ company.notes|truncatechars:50 }}</small>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                <div>
                                    {% if company.phone %}
                                        <i class="fas fa-phone text-primary me-1"></i>
                                        {{ company.phone }}
                                        <br>
                                    {% endif %}
                                    {% if company.email %}
                                        <i class="fas fa-envelope text-info me-1"></i>
                                        <a href="mailto:{{ company.email }}">{{ company.email }}</a>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                {% if company.website %}
                                    <a href="{{ company.website }}" target="_blank" class="text-decoration-none">
                                        <i class="fas fa-external-link-alt me-1"></i>
                                        زيارة الموقع
                                    </a>
                                {% else %}
                                    <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-info">{{ company.shipments.count }}</span>
                            </td>
                            <td>
                                {% if company.is_active %}
                                    <span class="badge bg-success">نشط</span>
                                {% else %}
                                    <span class="badge bg-secondary">غير نشط</span>
                                {% endif %}
                            </td>
                            <td>{{ company.created_at|date:"d/m/Y" }}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{% url 'shipping:company_detail' company.pk %}" class="btn btn-sm btn-outline-info" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'shipping:company_update' company.pk %}" class="btn btn-sm btn-outline-primary" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'shipping:rates' %}?company={{ company.pk }}" class="btn btn-sm btn-outline-success" title="الأسعار">
                                        <i class="fas fa-money-bill"></i>
                                    </a>
                                    <button class="btn btn-sm btn-outline-danger" onclick="confirmDelete({{ company.pk }}, '{{ company.name }}')" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if is_paginated %}
                <nav aria-label="تصفح الصفحات">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">الأولى</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">السابقة</a>
                            </li>
                        {% endif %}
                        
                        <li class="page-item active">
                            <span class="page-link">{{ page_obj.number }} من {{ page_obj.paginator.num_pages }}</span>
                        </li>
                        
                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">التالية</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">الأخيرة</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-building fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد شركات شحن</h4>
                <p class="text-muted">ابدأ بإضافة شركات الشحن التي تتعامل معها</p>
                <a href="{% url 'shipping:company_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    إضافة شركة جديدة
                </a>
            </div>
        {% endif %}
    </div>
</div>

<!-- Modal تأكيد الحذف -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف شركة الشحن <strong id="companyName"></strong>؟</p>
                <p class="text-danger small">تحذير: سيتم حذف جميع البيانات المرتبطة بهذه الشركة.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <a href="#" id="deleteLink" class="btn btn-danger">حذف</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete(companyId, companyName) {
    document.getElementById('companyName').textContent = companyName;
    document.getElementById('deleteLink').href = '/shipping/companies/' + companyId + '/delete/';
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}
