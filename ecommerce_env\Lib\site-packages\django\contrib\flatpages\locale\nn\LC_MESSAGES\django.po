# This file is distributed under the same license as the Django package.
#
# Translators:
# <AUTHOR> <EMAIL>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2011
# Sivert <PERSON>, 2021
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2021-11-12 08:48+0000\n"
"Last-Translator: Sivert <PERSON>\n"
"Language-Team: Norwegian Nynorsk (http://www.transifex.com/django/django/"
"language/nn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: nn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Advanced options"
msgstr "Avanserte innstillingar"

msgid "Flat Pages"
msgstr "Flatsider"

msgid "URL"
msgstr "Nettadresse"

msgid ""
"Example: “/about/contact/”. Make sure to have leading and trailing slashes."
msgstr ""
"Eksempel: “/om/kontakt/”. Kontroller at det er ein skråstrek framanfor og "
"bak."

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr ""
"Dette feltet kan berre innehalde bokstavar, nummer, skilleteikn, "
"understrekar, bindestrekar, skråstrekar eller tilder."

msgid "Example: “/about/contact”. Make sure to have a leading slash."
msgstr ""
"Eksempel: “/om/kontakt/”. Kontroller at det er ein skråstrek framanfor."

msgid "URL is missing a leading slash."
msgstr "Nettadressa manglar ein skråstrek framanfor."

msgid "URL is missing a trailing slash."
msgstr "Nettadressa manglar ein skråstrek bak."

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr "Flatside med nettadresse %(url)s eksisterer allereie for sida %(site)s"

msgid "title"
msgstr "tittel"

msgid "content"
msgstr "innhald"

msgid "enable comments"
msgstr "tillat kommentarer"

msgid "template name"
msgstr "malnamn"

msgid ""
"Example: “flatpages/contact_page.html”. If this isn’t provided, the system "
"will use “flatpages/default.html”."
msgstr ""
"Døme: “flatpages/contact_page.html”. Dersom denne ikkje er oppgjeve vert "
"“flatpages/default.html” brukt."

msgid "registration required"
msgstr "krevar registrering"

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr "Dersom denne er kryssa av, kan berre innlogga brukarar sjå sida."

msgid "sites"
msgstr "nettstadar"

msgid "flat page"
msgstr "flatside"

msgid "flat pages"
msgstr "flatsider"
