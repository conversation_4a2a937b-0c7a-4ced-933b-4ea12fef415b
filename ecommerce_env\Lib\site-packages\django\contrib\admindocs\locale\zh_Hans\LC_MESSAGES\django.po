# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2015
# Li<PERSON> <<EMAIL>>, 2016
# <AUTHOR> <EMAIL>, 2016
# <PERSON> <<EMAIL>>, 2014
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2020
# <PERSON> <<EMAIL>>, 2012
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2021-02-07 04:24+0000\n"
"Last-Translator: Veoco <<EMAIL>>\n"
"Language-Team: Chinese (China) (http://www.transifex.com/django/django/"
"language/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

msgid "Administrative Documentation"
msgstr "管理文档"

msgid "Home"
msgstr "首页"

msgid "Documentation"
msgstr "文档"

msgid "Bookmarklets"
msgstr "书签"

msgid "Documentation bookmarklets"
msgstr "文档书签"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"为了安装书签，将链接拖到你的书签工具条，或者鼠标右击链接然后把它添加到你的书"
"签中。现在你可以从这个站点的任何页面选择书签。"

msgid "Documentation for this page"
msgstr "关于本页面的文档"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr "从任何页面跳转到生成该页面的 view 文档。"

msgid "Tags"
msgstr "标签"

msgid "List of all the template tags and their functions."
msgstr "列出所有模板标签及其功能。"

msgid "Filters"
msgstr "过滤器"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr "过滤器可以应用到模板中的变量上来动态的改变输出。"

msgid "Models"
msgstr "模型"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"模型是系统中的所有的对象字段及其关联性的描述。每个模型都有其可以被模板变量访"
"问的字段列表"

msgid "Views"
msgstr "视图"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"由视图生成在公共站点的每个页面。视图定义了哪些模板用于生成页面和哪些对象可用"
"于该模板。"

msgid "Tools for your browser to quickly access admin functionality."
msgstr "工具为您的浏览器提供了快速访问和管理的功能。"

msgid "Please install docutils"
msgstr "请安装 docutils"

#, python-format
msgid ""
"The admin documentation system requires Python’s <a href=\"%(link)s"
"\">docutils</a> library."
msgstr "该管理文档系统需要 Python 的 <a href=\"%(link)s\">docutils</a> 库。"

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr "请询问你的管理员并安装 <a href=\"%(link)s\">docutils</a> 。"

#, python-format
msgid "Model: %(name)s"
msgstr "模型: %(name)s"

msgid "Fields"
msgstr "字段"

msgid "Field"
msgstr "字段"

msgid "Type"
msgstr "类型"

msgid "Description"
msgstr "描述"

msgid "Methods with arguments"
msgstr "方法和参数"

msgid "Method"
msgstr "方法"

msgid "Arguments"
msgstr "参数"

msgid "Back to Model documentation"
msgstr "返回到模型文档"

msgid "Model documentation"
msgstr "模型文档"

msgid "Model groups"
msgstr "模型组"

msgid "Templates"
msgstr "模板"

#, python-format
msgid "Template: %(name)s"
msgstr "模板: %(name)s"

#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr "模板：<q>%(name)s</q>"

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr "模板<q>%(name)s</q>的查找路径："

msgid "(does not exist)"
msgstr "(不存在)"

msgid "Back to Documentation"
msgstr "回到文档"

msgid "Template filters"
msgstr "模板过滤器"

msgid "Template filter documentation"
msgstr "模板过滤器文档"

msgid "Built-in filters"
msgstr "内置过滤器"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"要使用这些过滤器, 在你使用过滤器之前需要在模板中放置 <code>%(code)s</code> 。"

msgid "Template tags"
msgstr "模板标签"

msgid "Template tag documentation"
msgstr "模板标签文档"

msgid "Built-in tags"
msgstr "内置标签"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"要使用这些标签, 在你使用标签之前需要在模板中放置 <code>%(code)s</code> 。"

#, python-format
msgid "View: %(name)s"
msgstr "视图: %(name)s"

msgid "Context:"
msgstr "内容:"

msgid "Templates:"
msgstr "模板:"

msgid "Back to View documentation"
msgstr "回到视图文档"

msgid "View documentation"
msgstr "视图文档"

msgid "Jump to namespace"
msgstr "跳转到命名空间"

msgid "Empty namespace"
msgstr "空命名空间"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "按命名空间 %(name)s 排序视图"

msgid "Views by empty namespace"
msgstr "按空命名空间排序视图"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"查看函数：<code>%(full_name)s</code>. 名字：<code>%(url_name)s</code>.\n"
"\n"

msgid "tag:"
msgstr "标签："

msgid "filter:"
msgstr "过滤器："

msgid "view:"
msgstr "视图："

#, python-format
msgid "App %(app_label)r not found"
msgstr "应用 %(app_label)r 没有找到"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "在应用 %(app_label)r 里找不到模型 %(model_name)r"

msgid "model:"
msgstr "模型："

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "相关的 `%(app_label)s.%(data_type)s` 对象"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "相关的 `%(app_label)s.%(object_name)s` 对象"

#, python-format
msgid "all %s"
msgstr "所有 %s"

#, python-format
msgid "number of %s"
msgstr "%s 的数量"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s 似乎不是一个 urlpattern 对象"
