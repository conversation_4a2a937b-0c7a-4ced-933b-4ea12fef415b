from django.contrib import admin
from .models import Customer, Address


class AddressInline(admin.TabularInline):
    model = Address
    extra = 0


@admin.register(Customer)
class CustomerAdmin(admin.ModelAdmin):
    list_display = ['full_name', 'phone', 'email', 'orders_count', 'is_active', 'created_at']
    list_filter = ['gender', 'is_active', 'created_at']
    search_fields = ['first_name', 'last_name', 'phone', 'email']
    list_editable = ['is_active']
    ordering = ['-created_at']
    inlines = [AddressInline]

    fieldsets = (
        ('معلومات شخصية', {
            'fields': ('first_name', 'last_name', 'phone', 'email', 'gender', 'birth_date')
        }),
        ('معلومات إضافية', {
            'fields': ('notes', 'is_active')
        }),
    )

    def orders_count(self, obj):
        return obj.total_orders
    orders_count.short_description = 'عدد الطلبيات'


@admin.register(Address)
class AddressAdmin(admin.ModelAdmin):
    list_display = ['customer', 'city', 'wilaya', 'is_default']
    list_filter = ['wilaya', 'is_default']
    search_fields = ['customer__first_name', 'customer__last_name', 'street_address', 'city']
