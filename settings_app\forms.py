from django import forms
from .models import StoreSettings, OrderStatus, PaymentMethod


class StoreSettingsForm(forms.ModelForm):
    """نموذج إعدادات المتجر"""
    
    class Meta:
        model = StoreSettings
        fields = [
            'store_name', 'store_description', 'store_logo',
            'phone', 'email', 'address', 'website',
            'currency', 'tax_rate', 'default_shipping_cost',
            'free_shipping_threshold', 'low_stock_threshold',
            'enable_low_stock_alerts', 'enable_new_order_alerts',
            'invoice_prefix', 'invoice_footer'
        ]
        widgets = {
            'store_name': forms.TextInput(attrs={'placeholder': 'اسم المتجر'}),
            'store_description': forms.Textarea(attrs={'rows': 3, 'placeholder': 'وصف المتجر'}),
            'phone': forms.TextInput(attrs={'placeholder': 'رقم الهاتف'}),
            'email': forms.EmailInput(attrs={'placeholder': 'البريد الإلكتروني'}),
            'address': forms.Textarea(attrs={'rows': 3, 'placeholder': 'عنوان المتجر'}),
            'website': forms.URLInput(attrs={'placeholder': 'الموقع الإلكتروني'}),
            'tax_rate': forms.NumberInput(attrs={'step': '0.01', 'min': '0', 'max': '100'}),
            'default_shipping_cost': forms.NumberInput(attrs={'step': '0.01', 'min': '0'}),
            'free_shipping_threshold': forms.NumberInput(attrs={'step': '0.01', 'min': '0'}),
            'low_stock_threshold': forms.NumberInput(attrs={'min': '1'}),
            'invoice_prefix': forms.TextInput(attrs={'placeholder': 'INV'}),
            'invoice_footer': forms.Textarea(attrs={'rows': 3, 'placeholder': 'نص يظهر في أسفل الفاتورة'}),
        }
        labels = {
            'store_name': 'اسم المتجر',
            'store_description': 'وصف المتجر',
            'store_logo': 'شعار المتجر',
            'phone': 'رقم الهاتف',
            'email': 'البريد الإلكتروني',
            'address': 'العنوان',
            'website': 'الموقع الإلكتروني',
            'currency': 'العملة',
            'tax_rate': 'معدل الضريبة (%)',
            'default_shipping_cost': 'تكلفة الشحن الافتراضية',
            'free_shipping_threshold': 'حد الشحن المجاني',
            'low_stock_threshold': 'حد المخزون المنخفض',
            'enable_low_stock_alerts': 'تفعيل تنبيهات المخزون المنخفض',
            'enable_new_order_alerts': 'تفعيل تنبيهات الطلبات الجديدة',
            'invoice_prefix': 'بادئة رقم الفاتورة',
            'invoice_footer': 'تذييل الفاتورة',
        }
        help_texts = {
            'tax_rate': 'معدل الضريبة المضافة (إذا كان ينطبق)',
            'free_shipping_threshold': 'المبلغ الذي يحصل عنده الزبون على شحن مجاني',
            'low_stock_threshold': 'عدد القطع التي يتم عندها إرسال تنبيه نفاد المخزون',
            'invoice_prefix': 'البادئة التي تظهر قبل رقم الفاتورة',
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # إضافة CSS classes
        for field_name, field in self.fields.items():
            if field_name in ['enable_low_stock_alerts', 'enable_new_order_alerts']:
                field.widget.attrs['class'] = 'form-check-input'
            elif field_name in ['store_description', 'address', 'invoice_footer']:
                field.widget.attrs['class'] = 'form-control'
            elif field_name == 'store_logo':
                field.widget.attrs['class'] = 'form-control'
            else:
                field.widget.attrs['class'] = 'form-control'


class OrderStatusForm(forms.ModelForm):
    """نموذج حالة الطلب"""
    
    class Meta:
        model = OrderStatus
        fields = ['name', 'display_name', 'color', 'is_active', 'order']
        widgets = {
            'name': forms.TextInput(attrs={'placeholder': 'اسم الحالة (بالإنجليزية)'}),
            'display_name': forms.TextInput(attrs={'placeholder': 'الاسم المعروض'}),
            'color': forms.TextInput(attrs={'type': 'color'}),
            'order': forms.NumberInput(attrs={'min': '0'}),
        }
        labels = {
            'name': 'اسم الحالة',
            'display_name': 'الاسم المعروض',
            'color': 'لون الحالة',
            'is_active': 'نشط',
            'order': 'الترتيب',
        }
        help_texts = {
            'name': 'اسم الحالة باللغة الإنجليزية (للاستخدام في النظام)',
            'display_name': 'الاسم الذي يظهر للمستخدمين',
            'color': 'لون الحالة في الواجهة',
            'order': 'ترتيب ظهور الحالة (الأرقام الأصغر تظهر أولاً)',
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # إضافة CSS classes
        for field_name, field in self.fields.items():
            if field_name == 'is_active':
                field.widget.attrs['class'] = 'form-check-input'
            else:
                field.widget.attrs['class'] = 'form-control'


class PaymentMethodForm(forms.ModelForm):
    """نموذج طريقة الدفع"""
    
    class Meta:
        model = PaymentMethod
        fields = ['name', 'is_active', 'requires_confirmation', 'description']
        widgets = {
            'name': forms.TextInput(attrs={'placeholder': 'اسم طريقة الدفع'}),
            'description': forms.Textarea(attrs={'rows': 3, 'placeholder': 'وصف طريقة الدفع'}),
        }
        labels = {
            'name': 'اسم طريقة الدفع',
            'is_active': 'نشط',
            'requires_confirmation': 'يتطلب تأكيد',
            'description': 'الوصف',
        }
        help_texts = {
            'requires_confirmation': 'هل تحتاج هذه الطريقة لتأكيد من المدير؟',
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # إضافة CSS classes
        for field_name, field in self.fields.items():
            if field_name in ['is_active', 'requires_confirmation']:
                field.widget.attrs['class'] = 'form-check-input'
            elif field_name == 'description':
                field.widget.attrs['class'] = 'form-control'
            else:
                field.widget.attrs['class'] = 'form-control'
