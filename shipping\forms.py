from django import forms
from .models import Shipment, ShippingCompany, ShippingRate, ShippingZone


class ShipmentForm(forms.ModelForm):
    """نموذج إضافة/تعديل الشحنة"""
    
    class Meta:
        model = Shipment
        fields = [
            'order', 'company', 'tracking_number', 'status', 'weight',
            'shipping_cost', 'notes'
        ]
        widgets = {
            'tracking_number': forms.TextInput(attrs={'placeholder': 'رقم التتبع'}),
            'weight': forms.NumberInput(attrs={'step': '0.01', 'min': '0'}),
            'shipping_cost': forms.NumberInput(attrs={'step': '0.01', 'min': '0'}),
            'notes': forms.Textarea(attrs={'rows': 3, 'placeholder': 'ملاحظات حول الشحنة'}),
        }
        labels = {
            'order': 'الطلب',
            'company': 'شركة الشحن',
            'tracking_number': 'رقم التتبع',
            'status': 'حالة الشحنة',
            'weight': 'الوزن (كيلو)',
            'shipping_cost': 'تكلفة الشحن (دج)',
            'notes': 'ملاحظات',
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # تحسين عرض القوائم
        self.fields['company'].queryset = ShippingCompany.objects.filter(is_active=True)
        self.fields['company'].empty_label = 'اختر شركة الشحن'

        # إضافة CSS classes
        for field_name, field in self.fields.items():
            if field_name == 'notes':
                field.widget.attrs['class'] = 'form-control'
            else:
                field.widget.attrs['class'] = 'form-control'


class ShippingCompanyForm(forms.ModelForm):
    """نموذج إضافة/تعديل شركة الشحن"""
    
    class Meta:
        model = ShippingCompany
        fields = ['name', 'phone', 'email', 'website', 'notes', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'placeholder': 'اسم شركة الشحن'}),
            'phone': forms.TextInput(attrs={'placeholder': 'رقم الهاتف'}),
            'email': forms.EmailInput(attrs={'placeholder': 'البريد الإلكتروني'}),
            'website': forms.URLInput(attrs={'placeholder': 'الموقع الإلكتروني'}),
            'notes': forms.Textarea(attrs={'rows': 3, 'placeholder': 'ملاحظات حول الشركة'}),
        }
        labels = {
            'name': 'اسم الشركة',
            'phone': 'رقم الهاتف',
            'email': 'البريد الإلكتروني',
            'website': 'الموقع الإلكتروني',
            'notes': 'ملاحظات',
            'is_active': 'نشط',
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # إضافة CSS classes
        for field_name, field in self.fields.items():
            if field_name == 'is_active':
                field.widget.attrs['class'] = 'form-check-input'
            elif field_name == 'notes':
                field.widget.attrs['class'] = 'form-control'
            else:
                field.widget.attrs['class'] = 'form-control'


class ShippingZoneForm(forms.ModelForm):
    """نموذج إضافة/تعديل منطقة الشحن"""
    
    class Meta:
        model = ShippingZone
        fields = ['name', 'zone_type', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'placeholder': 'اسم المنطقة'}),
        }
        labels = {
            'name': 'اسم المنطقة',
            'zone_type': 'نوع المنطقة',
            'is_active': 'نشط',
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # إضافة CSS classes
        for field_name, field in self.fields.items():
            if field_name == 'is_active':
                field.widget.attrs['class'] = 'form-check-input'
            else:
                field.widget.attrs['class'] = 'form-control'


class ShippingRateForm(forms.ModelForm):
    """نموذج إضافة/تعديل سعر الشحن"""
    
    class Meta:
        model = ShippingRate
        fields = [
            'company', 'zone', 'base_price', 'price_per_kg',
            'free_shipping_threshold', 'delivery_days_min',
            'delivery_days_max', 'is_active'
        ]
        widgets = {
            'base_price': forms.NumberInput(attrs={'step': '0.01', 'min': '0'}),
            'price_per_kg': forms.NumberInput(attrs={'step': '0.01', 'min': '0'}),
            'free_shipping_threshold': forms.NumberInput(attrs={'step': '0.01', 'min': '0'}),
            'delivery_days_min': forms.NumberInput(attrs={'min': '1'}),
            'delivery_days_max': forms.NumberInput(attrs={'min': '1'}),
        }
        labels = {
            'company': 'شركة الشحن',
            'zone': 'المنطقة',
            'base_price': 'السعر الأساسي (دج)',
            'price_per_kg': 'السعر لكل كيلو (دج)',
            'free_shipping_threshold': 'حد الشحن المجاني (دج)',
            'delivery_days_min': 'أقل مدة توصيل (أيام)',
            'delivery_days_max': 'أقصى مدة توصيل (أيام)',
            'is_active': 'نشط',
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # تحسين عرض القوائم
        self.fields['company'].queryset = ShippingCompany.objects.filter(is_active=True)
        self.fields['zone'].queryset = ShippingZone.objects.filter(is_active=True)

        # إضافة CSS classes
        for field_name, field in self.fields.items():
            if field_name == 'is_active':
                field.widget.attrs['class'] = 'form-check-input'
            else:
                field.widget.attrs['class'] = 'form-control'
    
    def clean(self):
        """التحقق من صحة البيانات"""
        cleaned_data = super().clean()
        delivery_days_min = cleaned_data.get('delivery_days_min')
        delivery_days_max = cleaned_data.get('delivery_days_max')
        
        if delivery_days_min and delivery_days_max and delivery_days_min > delivery_days_max:
            raise forms.ValidationError('أقل مدة توصيل لا يمكن أن تكون أكبر من أقصى مدة توصيل')
        
        return cleaned_data


class ShipmentSearchForm(forms.Form):
    """نموذج البحث في الشحنات"""
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'placeholder': 'البحث في الشحنات...',
            'class': 'form-control'
        })
    )
    status = forms.ChoiceField(
        choices=[('', 'جميع الحالات')] + Shipment.STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    company = forms.ModelChoiceField(
        queryset=ShippingCompany.objects.filter(is_active=True),
        required=False,
        empty_label='جميع الشركات',
        widget=forms.Select(attrs={'class': 'form-select'})
    )
