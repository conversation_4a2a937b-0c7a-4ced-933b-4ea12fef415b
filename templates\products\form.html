{% extends 'base.html' %}

{% block title %}{{ title }} - مدير التجارة الإلكترونية{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item"><a href="{% url 'products:list' %}">المنتجات</a></li>
<li class="breadcrumb-item active">{{ title }}</li>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-box me-2"></i>
                    {{ title }}
                </h5>
            </div>
            <div class="card-body">
                <form method="post" enctype="multipart/form-data">
                    {% csrf_token %}
                    
                    <div class="row g-3">
                        <!-- اسم المنتج والفئة -->
                        <div class="col-md-8">
                            <label for="{{ form.name.id_for_label }}" class="form-label">{{ form.name.label }}</label>
                            {{ form.name }}
                            {% if form.name.errors %}
                                <div class="text-danger small">{{ form.name.errors.0 }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-4">
                            <label for="{{ form.category.id_for_label }}" class="form-label">{{ form.category.label }}</label>
                            {{ form.category }}
                            {% if form.category.errors %}
                                <div class="text-danger small">{{ form.category.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <!-- الوصف -->
                        <div class="col-12">
                            <label for="{{ form.description.id_for_label }}" class="form-label">{{ form.description.label }}</label>
                            {{ form.description }}
                            {% if form.description.errors %}
                                <div class="text-danger small">{{ form.description.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <!-- الأسعار ورمز المنتج -->
                        <div class="col-md-4">
                            <label for="{{ form.price.id_for_label }}" class="form-label">{{ form.price.label }}</label>
                            <div class="input-group">
                                {{ form.price }}
                                <span class="input-group-text">دج</span>
                            </div>
                            {% if form.price.errors %}
                                <div class="text-danger small">{{ form.price.errors.0 }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-4">
                            <label for="{{ form.cost_price.id_for_label }}" class="form-label">{{ form.cost_price.label }}</label>
                            <div class="input-group">
                                {{ form.cost_price }}
                                <span class="input-group-text">دج</span>
                            </div>
                            {% if form.cost_price.errors %}
                                <div class="text-danger small">{{ form.cost_price.errors.0 }}</div>
                            {% endif %}
                            <small class="text-muted">{{ form.cost_price.help_text }}</small>
                        </div>
                        <div class="col-md-4">
                            <label for="{{ form.sku.id_for_label }}" class="form-label">{{ form.sku.label }}</label>
                            {{ form.sku }}
                            {% if form.sku.errors %}
                                <div class="text-danger small">{{ form.sku.errors.0 }}</div>
                            {% endif %}
                            <small class="text-muted">{{ form.sku.help_text }}</small>
                        </div>
                        
                        <!-- الكميات -->
                        <div class="col-md-6">
                            <label for="{{ form.quantity.id_for_label }}" class="form-label">{{ form.quantity.label }}</label>
                            <div class="input-group">
                                {{ form.quantity }}
                                <span class="input-group-text">قطعة</span>
                            </div>
                            {% if form.quantity.errors %}
                                <div class="text-danger small">{{ form.quantity.errors.0 }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <label for="{{ form.min_quantity.id_for_label }}" class="form-label">{{ form.min_quantity.label }}</label>
                            <div class="input-group">
                                {{ form.min_quantity }}
                                <span class="input-group-text">قطعة</span>
                            </div>
                            {% if form.min_quantity.errors %}
                                <div class="text-danger small">{{ form.min_quantity.errors.0 }}</div>
                            {% endif %}
                            <small class="text-muted">{{ form.min_quantity.help_text }}</small>
                        </div>
                        
                        <!-- الصورة والحالة -->
                        <div class="col-md-8">
                            <label for="{{ form.image.id_for_label }}" class="form-label">{{ form.image.label }}</label>
                            {{ form.image }}
                            {% if form.image.errors %}
                                <div class="text-danger small">{{ form.image.errors.0 }}</div>
                            {% endif %}
                            {% if object and object.image %}
                                <div class="mt-2">
                                    <img src="{{ object.image.url }}" alt="{{ object.name }}" class="img-thumbnail" style="max-width: 200px;">
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-4 d-flex align-items-center">
                            <div class="form-check form-switch">
                                {{ form.is_active }}
                                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                    {{ form.is_active.label }}
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- معلومات إضافية للتعديل -->
                    {% if object %}
                        <hr>
                        <div class="row">
                            <div class="col-md-6">
                                <h6>معلومات الربح</h6>
                                {% if object.cost_price %}
                                    <p class="mb-1">
                                        <strong>هامش الربح:</strong> {{ object.profit_margin|floatformat:2 }} دج
                                    </p>
                                    <p class="mb-0">
                                        <strong>نسبة الربح:</strong> {{ object.profit_percentage|floatformat:1 }}%
                                    </p>
                                {% else %}
                                    <p class="text-muted">أدخل سعر التكلفة لحساب الأرباح</p>
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                <h6>حالة المخزون</h6>
                                {% if object.is_out_of_stock %}
                                    <span class="badge bg-danger">نفد من المخزون</span>
                                {% elif object.is_low_stock %}
                                    <span class="badge bg-warning text-dark">مخزون منخفض</span>
                                {% else %}
                                    <span class="badge bg-success">متوفر</span>
                                {% endif %}
                            </div>
                        </div>
                    {% endif %}
                    
                    <!-- أزرار الحفظ والإلغاء -->
                    <hr>
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ المنتج
                        </button>
                        <a href="{% url 'products:list' %}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                        {% if object %}
                            <a href="{% url 'products:detail' object.pk %}" class="btn btn-outline-info">
                                <i class="fas fa-eye me-2"></i>
                                عرض المنتج
                            </a>
                        {% endif %}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// حساب الربح تلقائياً
document.addEventListener('DOMContentLoaded', function() {
    const priceInput = document.getElementById('{{ form.price.id_for_label }}');
    const costPriceInput = document.getElementById('{{ form.cost_price.id_for_label }}');
    
    function calculateProfit() {
        const price = parseFloat(priceInput.value) || 0;
        const costPrice = parseFloat(costPriceInput.value) || 0;
        
        if (price > 0 && costPrice > 0) {
            const profit = price - costPrice;
            const profitPercentage = ((profit / costPrice) * 100).toFixed(1);
            
            // عرض النتيجة في مكان مناسب
            console.log(`الربح: ${profit.toFixed(2)} دج (${profitPercentage}%)`);
        }
    }
    
    if (priceInput && costPriceInput) {
        priceInput.addEventListener('input', calculateProfit);
        costPriceInput.addEventListener('input', calculateProfit);
    }
});
</script>
{% endblock %}
