# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON>, 2018-2019
# <PERSON>, 2011
# <PERSON> <<EMAIL>>, 2014
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON><PERSON> <razvan.s<PERSON><PERSON>@gmail.com>, 2015-2017
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2020-07-15 11:18+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Romanian (http://www.transifex.com/django/django/language/"
"ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?"
"2:1));\n"

msgid "Administrative Documentation"
msgstr "Documentație Administrativă"

msgid "Home"
msgstr "Acasă"

msgid "Documentation"
msgstr "Documentație"

msgid "Bookmarklets"
msgstr "Semne de carte"

msgid "Documentation bookmarklets"
msgstr "Semne de carte pentru documentație"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"Pentru a instala semne de carte, trageți link-ul în bara de semne de carte, "
"sau clic dreapta pe link și adăugați la semnele de carte. Acum puteți "
"selecta semnul de carte din orice pagină."

msgid "Documentation for this page"
msgstr "Documentație pentru pagina aceasta"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""
"Vă trimite de la orice pagină către documentația pentru codul de afișare "
"care generează acea pagină."

msgid "Tags"
msgstr "Taguri"

msgid "List of all the template tags and their functions."
msgstr "Lista tuturor tagurilor din șabloane și funcțiile lor."

msgid "Filters"
msgstr "Filtre"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"Filtrele sunt acțiuni care pot fi aplicate variabilelor într-un șablon "
"pentru a altera rezultatul."

msgid "Models"
msgstr "Modele"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"Modelele sunt descrieri ale tuturor obiectelor în sistem și a câmpurilor "
"asociate ale acestora. Fiecare model are o listă de câmpuri care pot fi "
"accesate ca variabile în șabloane."

msgid "Views"
msgstr "Coduri de afișare"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"Fiecare pagină din pagina web este generată de un cod de afișare. Codul de "
"afișare definește ce șablon este utlizat la generarea paginii și ce obiecte "
"sunt disponibile acelui șablon."

msgid "Tools for your browser to quickly access admin functionality."
msgstr ""
"Instrumente pentru browser pentru accesarea rapidă a funcționalității de "
"administrare."

msgid "Please install docutils"
msgstr "Instalați vă rugăm docutils"

#, python-format
msgid ""
"The admin documentation system requires Python's <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""
"Sistemul de documentație administrativă are nevoie de librăria Python <a "
"href=\"%(link)s\">docutils</a>."

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr "Rugați administratorii să instaleze <a href=\"%(link)s\">docutils</a>."

#, python-format
msgid "Model: %(name)s"
msgstr "Model: %(name)s"

msgid "Fields"
msgstr "Câmpuri"

msgid "Field"
msgstr "Câmp"

msgid "Type"
msgstr "Tip"

msgid "Description"
msgstr "Descriere"

msgid "Methods with arguments"
msgstr "Metode cu argumente"

msgid "Method"
msgstr "Metodă"

msgid "Arguments"
msgstr "Argumente"

msgid "Back to Model documentation"
msgstr "Înapoi la Documentația Modelelor"

msgid "Model documentation"
msgstr "Documentația modelelor"

msgid "Model groups"
msgstr "Grupuri de modele"

msgid "Templates"
msgstr "Șabloane"

#, python-format
msgid "Template: %(name)s"
msgstr "Șablonul: %(name)s"

#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr "Template: <q>%(name)s</q>"

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr "Calea de căutare pentru template <q>%(name)s</q>:"

msgid "(does not exist)"
msgstr "(inexistent)"

msgid "Back to Documentation"
msgstr "Înapoi la Documentație"

msgid "Template filters"
msgstr "Filtre pentru șabloane"

msgid "Template filter documentation"
msgstr "Documentația filtrelor pentru șabloane"

msgid "Built-in filters"
msgstr "Filtre implicite"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"Pentru a utiliza aceste filtre, puneți <code>%(code)s</code> în șablon "
"înainte de utilizarea acestui filtru."

msgid "Template tags"
msgstr "Taguri pentru șabloane"

msgid "Template tag documentation"
msgstr "Documentația tagurilor pentru șabloane"

msgid "Built-in tags"
msgstr "Taguri implicite"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"Pentru a utiliza aceste taguri, puneți <code>%(code)s</code> în șablon "
"înainte de a utiliza tagul."

#, python-format
msgid "View: %(name)s"
msgstr "Cod de afișare: %(name)s"

msgid "Context:"
msgstr "Context:"

msgid "Templates:"
msgstr "Șablonuri:"

msgid "Back to View documentation"
msgstr "Înapoi la Documentația Codurilor de afișare"

msgid "View documentation"
msgstr "Vezi documentația"

msgid "Jump to namespace"
msgstr "Mergeți la spațiul de nume"

msgid "Empty namespace"
msgstr "Spațiu de nume gol"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "Coduri de afișare după spațiul de nume %(name)s"

msgid "Views by empty namespace"
msgstr "Coduri de afișare după spațiu de nume gol"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"Funcția din codul de afișare: <code>%(full_name)s</code>. Nume: <code>"
"%(url_name)s</code>.\n"

msgid "tag:"
msgstr "tag:"

msgid "filter:"
msgstr "filtru:"

msgid "view:"
msgstr "cod de afișare:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "Aplicația %(app_label)r nu a fost găsită"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "Modelul %(model_name)r nu a fost găsit în aplicația %(app_label)r"

msgid "model:"
msgstr "model:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "obiectul înrudit `%(app_label)s.%(data_type)s`"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "obiectele înrudite `%(app_label)s.%(object_name)s`"

#, python-format
msgid "all %s"
msgstr "toate %s"

#, python-format
msgid "number of %s"
msgstr "numărul de %s"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s nu pare a fi un obiect urlpattern"
